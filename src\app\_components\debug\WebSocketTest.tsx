"use client";

import { useGameData } from "~/stores/game-store";
import { useClickBatcher } from "~/hooks/useClickBatcher";

export function WebSocketTest() {
  const gameData = useGameData();
  const { connected, error, pendingClicks } = useClickBatcher();

  if (!gameData.player) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 border border-gray-600 rounded-lg p-3 text-xs">
      <div className="font-semibold text-white mb-2">Debug Info</div>
      <div className="space-y-1 text-gray-300">
        <div>WebSocket: <span className={connected ? 'text-green-400' : 'text-red-400'}>{connected ? 'Connected' : 'Disconnected'}</span></div>
        <div>Pending Clicks: {pendingClicks}</div>
        <div>Balance: {gameData.getCurrentBalance().toFixed(2)} CTC</div>
        <div>Total Clicks: {gameData.player.stats.totalClicks}</div>
        {error && <div className="text-red-400">Error: {error}</div>}
      </div>
    </div>
  );
}