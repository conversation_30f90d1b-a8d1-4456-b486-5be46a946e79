"use client";

import { CurrencyType } from "~/types/game";
import { CURRENCY_CONFIGS } from "~/config/game-constants";

interface CurrencyDisplayProps {
  amount: number;
  currency: CurrencyType;
  showIcon?: boolean;
  className?: string;
}

const currencyConfig = {
  [CurrencyType.CRYPTO_COIN]: {
    symbol: "CTC",
    icon: "₿",
    color: "text-orange-400",
  },
  [CurrencyType.ETHEREUM_G]: {
    symbol: "ETHG",
    icon: "Ξ",
    color: "text-blue-400",
  },
  [CurrencyType.BYTE_COIN]: {
    symbol: "BTC",
    icon: "₿",
    color: "text-yellow-400",
  },
  [CurrencyType.SOLANA_X]: {
    symbol: "SOLX",
    icon: "◎",
    color: "text-purple-400",
  },
  [CurrencyType.CARDANO_Z]: {
    symbol: "ADAZ",
    icon: "₳",
    color: "text-blue-300",
  },
  [CurrencyType.POLKADOT_Y]: {
    symbol: "DOTY",
    icon: "●",
    color: "text-pink-400",
  },
  [CurrencyType.AVALANCHE_A]: {
    symbol: "AVAX",
    icon: "A",
    color: "text-red-400",
  },
  [CurrencyType.COSMOS_C]: {
    symbol: "ATOM",
    icon: "⚛",
    color: "text-indigo-400",
  },
  [CurrencyType.TEZOS_T]: {
    symbol: "XTZ",
    icon: "ꜩ",
    color: "text-cyan-400",
  },
  [CurrencyType.ALGORAND_ALGO]: {
    symbol: "ALGO",
    icon: "Ⱥ",
    color: "text-gray-300",
  },
};

export function CurrencyDisplay({ 
  amount, 
  currency, 
  showIcon = false, 
  className = "" 
}: CurrencyDisplayProps) {
  const config = currencyConfig[currency] || {
    symbol: "???",
    icon: "$",
    color: "text-gray-400",
  };
  
  // Get additional info from game constants if available
  const gameConfig = CURRENCY_CONFIGS[currency];
  const symbol = gameConfig?.symbol || config.symbol;
  
  const formatAmount = (value: number): string => {
    if (value >= 1e12) return `${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `${(value / 1e3).toFixed(2)}K`;
    return value.toFixed(2);
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {showIcon && (
        <span className={`font-bold ${config.color}`}>
          {config.icon}
        </span>
      )}
      <span className={`font-medium ${config.color}`}>
        {formatAmount(amount)}
      </span>
      <span className="text-gray-400 text-sm">
        {symbol}
      </span>
    </div>
  );
}