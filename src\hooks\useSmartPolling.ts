import { useEffect, useRef, useCallback } from 'react';
import { useGameStore } from '~/stores/game-store';

interface SmartPollingOptions {
  interval?: number; // Default 10 seconds
  pauseOnActivity?: boolean; // Pause when user is actively clicking
  pauseOnHidden?: boolean; // Pause when tab is hidden
}

export function useSmartPolling(
  refetchFn: () => void,
  options: SmartPollingOptions = {}
) {
  const {
    interval = 10000, // 10 seconds default
    pauseOnActivity = true,
    pauseOnHidden = true,
  } = options;

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const isVisibleRef = useRef<boolean>(true);
  const store = useGameStore();

  // Track user activity
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
  }, []);

  // Check if we should poll
  const shouldPoll = useCallback(() => {
    // Don't poll if tab is hidden
    if (pauseOnHidden && !isVisibleRef.current) {
      return false;
    }

    // Don't poll if user was recently active (within 5 seconds)
    if (pauseOnActivity) {
      const timeSinceActivity = Date.now() - lastActivityRef.current;
      if (timeSinceActivity < 5000) {
        return false;
      }
    }

    return true;
  }, [pauseOnActivity, pauseOnHidden]);

  // Start polling
  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      if (shouldPoll()) {
        refetchFn();
      }
    }, interval);
  }, [interval, shouldPoll, refetchFn]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Handle visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;

      if (isVisibleRef.current) {
        // Tab became visible, restart polling but don't fetch immediately
        // This prevents balance jumps when switching tabs
        console.log('🔄 Tab became visible, restarting polling (no immediate fetch)');
        startPolling();
      } else {
        // Tab became hidden, stop polling
        console.log('⏸️ Tab became hidden, stopping polling');
        stopPolling();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [startPolling, stopPolling]);

  // Track clicks to update activity
  useEffect(() => {
    const unsubscribe = useGameStore.subscribe(
      (state) => state.pendingClicks,
      (pendingClicks) => {
        if (pendingClicks > 0) {
          updateActivity();
        }
      }
    );

    return unsubscribe;
  }, [updateActivity]);

  // Start polling on mount
  useEffect(() => {
    startPolling();
    return stopPolling;
  }, [startPolling, stopPolling]);

  // Track mouse/keyboard activity
  useEffect(() => {
    const events = ['mousedown', 'keydown', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity);
      });
    };
  }, [updateActivity]);

  return {
    startPolling,
    stopPolling,
    updateActivity,
  };
}