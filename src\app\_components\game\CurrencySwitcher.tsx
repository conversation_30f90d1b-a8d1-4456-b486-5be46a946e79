"use client";

import { useState } from "react";
import { CurrencyType } from "~/types/game";
import { api } from "~/trpc/react";
import { useGameData } from "~/stores/game-store";
import { ArrowPathIcon, LockClosedIcon } from "@heroicons/react/24/outline";
import { formatCurrencyWithSymbol } from "~/lib/game-utils";

export function CurrencySwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const gameData = useGameData();
  const { data: availableCurrencies, isLoading } = api.game.getAvailableCurrencies.useQuery();
  
  const switchMiningTarget = api.game.switchMiningTarget.useMutation({
    onSuccess: () => {
      gameData.refetch();
      setIsOpen(false);
    },
  });

  const activeCurrency = gameData.player?.activeMiningCurrency || CurrencyType.CRYPTO_COIN;
  const activeCurrencyData = availableCurrencies?.find(c => c.type === activeCurrency);

  const handleSwitchCurrency = (currency: CurrencyType) => {
    if (currency === activeCurrency) return;
    switchMiningTarget.mutate({ currency });
  };

  if (isLoading || !availableCurrencies) {
    return (
      <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-600 rounded-full animate-pulse"></div>
          <span className="text-gray-400">Loading currencies...</span>
        </div>
        <ArrowPathIcon className="w-5 h-5 text-gray-500 animate-spin" />
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Current Currency Display */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 w-full flex items-center justify-between hover:bg-gray-700/50 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <div 
            className="w-6 h-6 rounded-full" 
            style={{ backgroundColor: activeCurrencyData?.color || '#f7931a' }}
          ></div>
          <div className="flex flex-col text-left">
            <span className="text-white font-medium">{activeCurrencyData?.name || 'CryptoCoin'}</span>
            <span className="text-xs text-gray-400">Mining Target</span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-white font-medium">
            {formatCurrencyWithSymbol(
              gameData.getCurrencyBalance(activeCurrency),
              activeCurrency
            )}
          </span>
          <svg 
            className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      {/* Currency Dropdown */}
      {isOpen && (
        <div className="absolute z-10 mt-2 w-full bg-gray-800 border border-gray-700 rounded-lg shadow-lg overflow-hidden">
          <div className="max-h-64 overflow-y-auto">
            {availableCurrencies.map((currency) => (
              <button
                key={currency.type}
                onClick={() => currency.isUnlocked && handleSwitchCurrency(currency.type)}
                disabled={!currency.isUnlocked || switchMiningTarget.isPending}
                className={`w-full p-3 flex items-center justify-between hover:bg-gray-700/50 transition-colors ${
                  currency.type === activeCurrency ? 'bg-purple-900/30' : ''
                } ${!currency.isUnlocked ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-5 h-5 rounded-full" 
                    style={{ backgroundColor: currency.color }}
                  ></div>
                  <div className="flex flex-col text-left">
                    <span className="text-white font-medium">{currency.name}</span>
                    <span className="text-xs text-gray-400">
                      {currency.isUnlocked 
                        ? `${currency.miningEfficiency.toFixed(2)}x mining efficiency` 
                        : 'Locked - Research required'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {!currency.isUnlocked ? (
                    <LockClosedIcon className="w-4 h-4 text-gray-500" />
                  ) : (
                    <span className="text-sm text-gray-300">
                      {formatCurrencyWithSymbol(
                        gameData.getCurrencyBalance(currency.type),
                        currency.type
                      )}
                    </span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}