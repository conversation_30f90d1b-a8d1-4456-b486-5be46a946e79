"use client";

import { useState, useEffect } from "react";
import { useGameStore } from "~/stores/game-store";
import { usePassiveIncome } from "~/hooks/usePassiveIncome";

export function PassiveIncomeDebug() {
  const [isVisible, setIsVisible] = useState(false);
  const [lastServerBalance, setLastServerBalance] = useState(0);
  
  const {
    player,
    miningRigs,
    optimisticBalance,
    passiveIncomeActive,
    lastPassiveUpdate,
    getCurrentBalance,
    getTotalCPS,
  } = useGameStore();
  
  const { isActive, cps } = usePassiveIncome();

  // Track server balance changes
  useEffect(() => {
    if (player) {
      setLastServerBalance(player.currencies.cryptoCoin);
    }
  }, [player?.currencies.cryptoCoin]);

  // Toggle visibility with Ctrl+Shift+D
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrl<PERSON><PERSON> && e.shiftKey && e.key === 'D') {
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <div className="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded">
          Press Ctrl+Shift+D for debug info
        </div>
      </div>
    );
  }

  const balanceDiff = optimisticBalance - lastServerBalance;
  const timeSinceUpdate = Date.now() - lastPassiveUpdate;

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-gray-900 border border-gray-700 rounded-lg p-4 text-xs text-white max-w-sm">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-green-400">Passive Income Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-1">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <div className="text-gray-400">Server Balance:</div>
            <div className="font-mono">{lastServerBalance.toFixed(2)}</div>
          </div>
          <div>
            <div className="text-gray-400">Optimistic Balance:</div>
            <div className="font-mono">{optimisticBalance.toFixed(2)}</div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <div>
            <div className="text-gray-400">Current Balance:</div>
            <div className="font-mono">{getCurrentBalance().toFixed(2)}</div>
          </div>
          <div>
            <div className="text-gray-400">Difference:</div>
            <div className={`font-mono ${balanceDiff > 0 ? 'text-green-400' : balanceDiff < 0 ? 'text-red-400' : 'text-gray-400'}`}>
              {balanceDiff > 0 ? '+' : ''}{balanceDiff.toFixed(2)}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <div className="text-gray-400">CPS (Store):</div>
            <div className="font-mono">{getTotalCPS().toFixed(2)}</div>
          </div>
          <div>
            <div className="text-gray-400">CPS (Hook):</div>
            <div className="font-mono">{cps.toFixed(2)}</div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <div className="text-gray-400">Passive Active (Store):</div>
            <div className={passiveIncomeActive ? 'text-green-400' : 'text-red-400'}>
              {passiveIncomeActive ? 'Yes' : 'No'}
            </div>
          </div>
          <div>
            <div className="text-gray-400">Passive Active (Hook):</div>
            <div className={isActive ? 'text-green-400' : 'text-red-400'}>
              {isActive ? 'Yes' : 'No'}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <div className="text-gray-400">Mining Rigs:</div>
            <div className="font-mono">{miningRigs.length}</div>
          </div>
          <div>
            <div className="text-gray-400">Last Update:</div>
            <div className="font-mono">{(timeSinceUpdate / 1000).toFixed(1)}s ago</div>
          </div>
        </div>

        <div className="mt-2 pt-2 border-t border-gray-700">
          <div className="text-gray-400 mb-1">Debug Info:</div>
          <div className="text-xs space-y-1">
            <div>Last Fetch: {((Date.now() - (useGameStore.getState().lastFetch || 0)) / 1000).toFixed(1)}s ago</div>
            <div>Balance Trend: {balanceDiff > 0.1 ? '📈 Growing' : balanceDiff < -0.1 ? '📉 Decreasing' : '➡️ Stable'}</div>
          </div>
        </div>

        {miningRigs.length > 0 && (
          <div className="mt-2 pt-2 border-t border-gray-700">
            <div className="text-gray-400 mb-1">Mining Rigs:</div>
            {miningRigs.map((rig, index) => (
              <div key={index} className="text-xs">
                {rig.rigType}: {rig.quantity}x L{rig.level} 
                ({(rig.baseOutput * rig.level * rig.quantity * rig.efficiency).toFixed(2)} CPS)
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
