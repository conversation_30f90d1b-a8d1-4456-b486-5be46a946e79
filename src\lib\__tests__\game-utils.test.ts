import { describe, test, expect } from 'vitest';
import {
  calculateTotalCPS,
  calculateRigCost,
  calculateUpgradeCost,
  calculateOfflineProduction,
  calculateClickPower,
  applyShardBonuses,
  validateClickRate,
  validateClickPattern,
} from '../game-utils';
import { decimalToNumber, numberToDecimal } from '../game-utils-server';
import { RigType, ShardRarity, ShardCategory } from '../../types/game';
import type { MiningRig, PlayerShard, BlockchainShard } from '../../types/game';
import { Decimal } from '@prisma/client/runtime/library';

// Mock data for tests
const mockMiningRigs: MiningRig[] = [
  {
    id: '1',
    playerId: 'player1',
    rigType: RigType.USB_ASIC,
    level: 1,
    quantity: 5,
    baseOutput: new Decimal(0.1),
    efficiency: new Decimal(1.0),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '2',
    playerId: 'player1',
    rigType: RigType.GPU_FARM,
    level: 2,
    quantity: 2,
    baseOutput: new Decimal(1.0),
    efficiency: new Decimal(1.2),
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockShard: BlockchainShard = {
  id: 'shard1',
  name: 'Mining Boost Shard',
  rarity: ShardRarity.RARE,
  category: ShardCategory.MINING_BOOST,
  effects: [
    { type: 'MULTIPLIER', target: 'mining_output', value: 1.5 },
    { type: 'FLAT_BONUS', target: 'click_power', value: 2 },
  ],
  description: 'Increases mining output by 50%',
};

const mockPlayerShards: PlayerShard[] = [
  {
    id: 'ps1',
    playerId: 'player1',
    shardId: 'shard1',
    shard: mockShard,
    quantity: 1,
    equipped: true,
    acquiredAt: new Date(),
  },
];

describe('Mining Calculations', () => {
  test('should calculate correct total CPS from mining rigs', () => {
    const totalCPS = calculateTotalCPS(mockMiningRigs);
    
    // USB_ASIC: 0.1 * 1.0 * 1 * 5 = 0.5
    // GPU_FARM: 1.0 * 1.2 * 2 * 2 = 4.8
    // Total: 0.5 + 4.8 = 5.3
    expect(totalCPS).toBe(5.3);
  });

  test('should calculate correct rig purchase cost', () => {
    const cost = calculateRigCost('USB_ASIC', 0, 3);
    
    // Base cost: 10, multiplier: 1.15
    // First rig: 10 * 1.15^0 = 10
    // Second rig: 10 * 1.15^1 = 11.5
    // Third rig: 10 * 1.15^2 = 13.225
    // Total: 10 + 11.5 + 13.225 = 34.725
    expect(cost).toBeCloseTo(34.725, 2);
  });

  test('should calculate correct upgrade cost', () => {
    const cost = calculateUpgradeCost('USB_ASIC', 1, 2);
    
    // Base cost: 10, upgrade multiplier: 1.5
    // Level 1 to 2: 10 * 1.5^1 = 15
    // Level 2 to 3: 10 * 1.5^2 = 22.5
    // Total: 15 + 22.5 = 37.5
    expect(cost).toBeCloseTo(37.5, 2);
  });

  test('should apply shard bonuses correctly', () => {
    const baseValue = 100;
    const result = applyShardBonuses(baseValue, mockPlayerShards, 'mining_output');
    
    // Base: 100, Multiplier: 1.5
    // Result: 100 * 1.5 = 150
    expect(result).toBe(150);
  });

  test('should calculate click power with bonuses', () => {
    const baseClickPower = 1;
    const clickPower = calculateClickPower(mockPlayerShards, baseClickPower);
    
    // Base: 1, Flat bonus: 2
    // Result: (1 + 2) * 1 = 3
    expect(clickPower).toBe(3);
  });

  test('should calculate offline production with efficiency penalty', () => {
    const offlineTimeSeconds = 10 * 3600; // 10 hours
    const production = calculateOfflineProduction(mockMiningRigs, mockPlayerShards, offlineTimeSeconds);
    
    const baseCPS = calculateTotalCPS(mockMiningRigs); // 5.3
    const bonusedCPS = applyShardBonuses(baseCPS, mockPlayerShards, 'offline_production'); // 5.3 * 1.5 = 7.95
    const maxOfflineHours = 24;
    const effectiveHours = Math.min(10, maxOfflineHours); // 10
    const efficiency = 0.8; // Penalty after 8 hours
    
    const expected = bonusedCPS * effectiveHours * 3600 * efficiency;
    expect(production).toBeCloseTo(expected, 2);
  });

  test('should calculate offline production without penalty for short periods', () => {
    const offlineTimeSeconds = 4 * 3600; // 4 hours
    const production = calculateOfflineProduction(mockMiningRigs, mockPlayerShards, offlineTimeSeconds);
    
    const baseCPS = calculateTotalCPS(mockMiningRigs); // 5.3
    const bonusedCPS = applyShardBonuses(baseCPS, mockPlayerShards, 'offline_production'); // 5.3 * 1.5 = 7.95
    const effectiveHours = 4;
    const efficiency = 1.0; // No penalty for <= 8 hours
    
    const expected = bonusedCPS * effectiveHours * 3600 * efficiency;
    expect(production).toBeCloseTo(expected, 2);
  });
});

describe('Click Rate Validation', () => {
  test('should validate normal click rate', () => {
    const now = Date.now();
    const clicks = [
      now - 500,
      now - 400,
      now - 300,
      now - 200,
      now - 100,
    ];
    
    expect(validateClickRate(clicks)).toBe(true);
  });

  test('should reject excessive click rate', () => {
    const now = Date.now();
    const clicks = Array.from({ length: 25 }, (_, i) => now - i * 10); // 25 clicks in 250ms
    
    expect(validateClickRate(clicks)).toBe(false);
  });

  test('should validate clicks at the limit', () => {
    const now = Date.now();
    const clicks = Array.from({ length: 20 }, (_, i) => now - i * 50); // 20 clicks in 1 second
    
    expect(validateClickRate(clicks)).toBe(true);
  });

  test('should handle invalid click data', () => {
    expect(validateClickRate([])).toBe(true);
    expect(validateClickRate([null as any])).toBe(true);
    expect(validateClickRate(['invalid' as any])).toBe(true);
  });
});

describe('Click Pattern Validation', () => {
  test('should validate human-like click patterns', () => {
    const now = Date.now();
    const humanClicks = [
      now - 1000,
      now - 850,
      now - 650,
      now - 400,
      now - 150,
    ];
    
    expect(validateClickPattern(humanClicks)).toBe(true);
  });

  test('should reject suspiciously consistent patterns', () => {
    const now = Date.now();
    const botClicks = Array.from({ length: 10 }, (_, i) => now - i * 50); // Exactly 50ms apart
    
    expect(validateClickPattern(botClicks)).toBe(false);
  });

  test('should reject impossibly fast clicking', () => {
    const now = Date.now();
    const tooFastClicks = Array.from({ length: 10 }, (_, i) => now - i * 20); // 20ms apart
    
    expect(validateClickPattern(tooFastClicks)).toBe(false);
  });

  test('should allow fast but varied clicking', () => {
    const now = Date.now();
    const fastButVariedClicks = [
      now - 1000,
      now - 920,
      now - 850,
      now - 750,
      now - 680,
      now - 580,
      now - 500,
    ];
    
    expect(validateClickPattern(fastButVariedClicks)).toBe(true);
  });

  test('should handle insufficient data', () => {
    expect(validateClickPattern([])).toBe(true);
    expect(validateClickPattern([Date.now()])).toBe(true);
    expect(validateClickPattern([Date.now(), Date.now() - 100])).toBe(true);
  });
});

describe('Utility Functions', () => {
  test('should convert Decimal to number correctly', () => {
    const decimal = new Decimal(123.456);
    expect(decimalToNumber(decimal)).toBe(123.456);
    
    expect(decimalToNumber(789.123)).toBe(789.123);
    expect(decimalToNumber('456.789')).toBe(456.789);
    expect(decimalToNumber({} as any)).toBe(0);
  });

  test('should convert number to Decimal correctly', () => {
    const result = numberToDecimal(123.456);
    expect(result).toBeInstanceOf(Decimal);
    expect(result.toNumber()).toBe(123.456);
  });
});

describe('Shard Effect Application', () => {
  test('should apply multiple effects correctly', () => {
    const multiEffectShard: BlockchainShard = {
      id: 'shard2',
      name: 'Multi Effect Shard',
      rarity: ShardRarity.EPIC,
      category: ShardCategory.EFFICIENCY,
      effects: [
        { type: 'FLAT_BONUS', target: 'mining_output', value: 10 },
        { type: 'MULTIPLIER', target: 'mining_output', value: 1.2 },
      ],
      description: 'Multiple effects shard',
    };

    const playerShards: PlayerShard[] = [
      {
        id: 'ps2',
        playerId: 'player1',
        shardId: 'shard2',
        shard: multiEffectShard,
        quantity: 1,
        equipped: true,
        acquiredAt: new Date(),
      },
    ];

    const baseValue = 100;
    const result = applyShardBonuses(baseValue, playerShards, 'mining_output');
    
    // Base: 100, Flat bonus: +10, then Multiplier: *1.2
    // Result: (100 + 10) * 1.2 = 132
    expect(result).toBe(132);
  });

  test('should handle quantity multipliers for shards', () => {
    const playerShards: PlayerShard[] = [
      {
        id: 'ps3',
        playerId: 'player1',
        shardId: 'shard1',
        shard: mockShard,
        quantity: 3, // Multiple copies
        equipped: true,
        acquiredAt: new Date(),
      },
    ];

    const baseValue = 100;
    const result = applyShardBonuses(baseValue, playerShards, 'click_power');
    
    // Base: 100, Flat bonus: 2 * 3 = 6
    // Result: (100 + 6) * 1 = 106
    expect(result).toBe(106);
  });

  test('should ignore unequipped shards', () => {
    const playerShards: PlayerShard[] = [
      {
        id: 'ps4',
        playerId: 'player1',
        shardId: 'shard1',
        shard: mockShard,
        quantity: 1,
        equipped: false, // Not equipped
        acquiredAt: new Date(),
      },
    ];

    const baseValue = 100;
    const result = applyShardBonuses(baseValue, playerShards, 'mining_output');
    
    // Should return base value since shard is not equipped
    expect(result).toBe(100);
  });

  test('should handle target-specific effects', () => {
    const baseValue = 100;
    
    // Test with matching target
    const resultMatching = applyShardBonuses(baseValue, mockPlayerShards, 'mining_output');
    expect(resultMatching).toBe(150); // 100 * 1.5
    
    // Test with non-matching target
    const resultNonMatching = applyShardBonuses(baseValue, mockPlayerShards, 'research_speed');
    expect(resultNonMatching).toBe(100); // No effect
  });
});