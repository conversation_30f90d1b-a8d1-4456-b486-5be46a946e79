import React from 'react';
import { io, Socket } from 'socket.io-client';

interface ClickEvent {
  timestamp: number;
  clickPower?: number;
}

interface GameUpdateEvent {
  balance: number;
  totalClicks: number;
  cps: number;
}

interface WebSocketEvents {
  // Client to Server
  click: ClickEvent;
  join_game: { userId: string };
  
  // Server to Client
  game_update: GameUpdateEvent;
  click_acknowledged: { earnedAmount: number; newBalance: number };
  rate_limit_warning: { message: string };
  error: { message: string };
}

class GameWebSocketClient {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isConnected = false;
  private currentUserId: string | null = null;
  private isConnecting = false;

  async connect(userId: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      // Prevent multiple simultaneous connections
      if (this.isConnecting) {
        console.log('🔧 Connection already in progress, waiting...');
        // Wait a bit and try again
        setTimeout(() => {
          if (this.isConnected && this.currentUserId === userId) {
            resolve();
          } else {
            reject(new Error('Connection in progress'));
          }
        }, 1000);
        return;
      }

      // If already connected to the same user, don't reconnect
      if (this.socket?.connected && this.currentUserId === userId) {
        console.log('🔧 Already connected for user:', userId);
        resolve();
        return;
      }

      this.isConnecting = true;

      // Disconnect any existing connection first
      if (this.socket) {
        console.log('🔧 Disconnecting existing WebSocket connection...');
        this.socket.disconnect();
        this.socket = null;
        this.isConnected = false;
        this.currentUserId = null;
      }

      // Initialize the WebSocket server first
      try {
        console.log('🔧 Initializing WebSocket server...');
        const response = await fetch('/api/socket');
        console.log('🔧 WebSocket server initialization response:', response.status);
      } catch (error) {
        console.warn('Failed to initialize WebSocket server:', error);
      }

      // Get JWT token for authentication BEFORE connecting
      let token: string | null = null;
      if (userId !== 'anonymous') {
        try {
          console.log('🔐 Getting WebSocket authentication token for user:', userId);
          const tokenResponse = await fetch('/api/websocket/token', {
            method: 'POST',
            credentials: 'include', // Include cookies for session
          });

          console.log('🔐 Token response status:', tokenResponse.status);

          if (tokenResponse.ok) {
            const tokenData = await tokenResponse.json();
            token = tokenData.token;
            console.log('✅ WebSocket token obtained successfully');
          } else {
            const errorData = await tokenResponse.json().catch(() => ({ error: 'Unknown error' }));
            console.warn('⚠️ Failed to get WebSocket token:', errorData);
            console.warn('⚠️ Will connect as unauthenticated due to token failure');
            // Don't fall back to anonymous, keep the userId but without token
          }
        } catch (error) {
          console.warn('⚠️ Error getting WebSocket token:', error);
          console.warn('⚠️ Will connect as unauthenticated due to error');
          // Don't fall back to anonymous, keep the userId but without token
        }
      } else {
        console.log('🔧 Connecting as anonymous user');
      }

      console.log('🔧 Connecting to WebSocket with userId:', userId, 'Token:', token ? 'Present' : 'Missing');

      this.socket = io('/game', {
        path: '/api/socket',
        auth: {
          userId,
          token, // Include JWT token (may be null)
        },
        transports: ['websocket', 'polling'], // Allow fallback to polling
        timeout: 10000,
        forceNew: true, // Force new connection
      });

      this.socket.on('connect', () => {
        console.log('🔗 Connected to game WebSocket for user:', userId);
        this.isConnected = true;
        this.isConnecting = false;
        this.currentUserId = userId;
        this.reconnectAttempts = 0;
        this.socket?.emit('join_game', { userId });
        resolve();
      });

      this.socket.on('disconnect', (reason) => {
        console.log('🔌 Disconnected from game WebSocket:', reason);
        this.isConnected = false;
        this.isConnecting = false;
        this.currentUserId = null;

        if (reason === 'io server disconnect') {
          // Server disconnected us, try to reconnect
          this.handleReconnect(userId);
        }
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ WebSocket connection error:', error);
        this.isConnected = false;
        this.isConnecting = false;
        this.currentUserId = null;
        reject(error);
      });

      // Set up event listeners
      this.setupEventListeners();
    });
  }

  private handleReconnect(userId: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 10000);
      
      console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
      
      setTimeout(() => {
        this.connect(userId).catch(console.error);
      }, delay);
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('game_update', (data: GameUpdateEvent) => {
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('game_update', { detail: data }));
    });

    this.socket.on('click_acknowledged', (data) => {
      window.dispatchEvent(new CustomEvent('click_acknowledged', { detail: data }));
    });

    this.socket.on('rate_limit_warning', (data) => {
      window.dispatchEvent(new CustomEvent('rate_limit_warning', { detail: data }));
    });

    this.socket.on('error', (data) => {
      console.error('🚨 Game WebSocket error:', data.message);
      window.dispatchEvent(new CustomEvent('game_error', { detail: data }));
    });
  }

  sendClick(timestamp: number = Date.now()) {
    if (!this.isConnected || !this.socket) {
      console.warn('⚠️ Cannot send click - WebSocket not connected');
      return false;
    }

    this.socket.emit('click', { timestamp });
    return true;
  }

  disconnect() {
    if (this.socket) {
      console.log('🔌 Manually disconnecting WebSocket');
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.isConnecting = false;
      this.currentUserId = null;
    }
  }

  get connected() {
    return this.isConnected;
  }
}

// Singleton instance
export const gameWebSocket = new GameWebSocketClient();

// Hook for React components
export function useGameWebSocket(userId: string | null) {
  const [connected, setConnected] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    // Don't connect if userId is null (session still loading)
    if (!userId) {
      console.log('🔧 Waiting for userId to be determined...');
      setConnected(false);
      setError(null);
      return;
    }

    console.log('🔧 Connecting WebSocket for userId:', userId);

    gameWebSocket.connect(userId)
      .then(() => {
        setConnected(true);
        setError(null);
      })
      .catch((err) => {
        setConnected(false);
        setError(err.message);
      });

    const handleError = (event: CustomEvent) => setError(event.detail.message);
    window.addEventListener('game_error', handleError as EventListener);

    return () => {
      window.removeEventListener('game_error', handleError as EventListener);
    };
  }, [userId]);

  return {
    connected,
    error,
    sendClick: gameWebSocket.sendClick.bind(gameWebSocket),
    disconnect: gameWebSocket.disconnect.bind(gameWebSocket),
  };
}