"use client";

import { CpuChipIcon, ArrowUpIcon, LockClosedIcon, StarIcon } from "@heroicons/react/24/outline";
import { CurrencyDisplay } from "./CurrencyDisplay";
import { CurrencyType } from "~/types/game";

interface UnlockRequirement {
  type: string;
  currency?: CurrencyType;
  value: number;
}

interface RigCardProps {
  name: string;
  description?: string;
  tier: number;
  level: number;
  quantity: number;
  maxLevel: number;
  output: number; // CTC per second per rig
  totalOutput: number; // Total CTC per second from all rigs of this type
  upgradeCost?: number;
  purchaseCost?: number;
  isUnlocked: boolean;
  unlockRequirement?: UnlockRequirement;
  onUpgrade?: () => void;
  onPurchase?: () => void;
  canAffordUpgrade?: boolean;
  canAffordPurchase?: boolean;
  className?: string;
}

export function RigCard({
  name,
  description,
  tier,
  level,
  quantity,
  maxLevel,
  output,
  totalOutput,
  upgradeCost,
  purchaseCost,
  isUnlocked,
  unlockRequirement,
  onUpgrade,
  onPurchase,
  canAffordUpgrade = false,
  canAffordPurchase = false,
  className = "",
}: RigCardProps) {
  // Get tier color based on tier level
  const getTierColor = (tier: number) => {
    const colors = [
      'text-gray-400 bg-gray-500/20', // Tier 1
      'text-green-400 bg-green-500/20', // Tier 2
      'text-blue-400 bg-blue-500/20', // Tier 3
      'text-purple-400 bg-purple-500/20', // Tier 4
      'text-orange-400 bg-orange-500/20', // Tier 5
      'text-red-400 bg-red-500/20', // Tier 6
    ];
    return colors[tier - 1] || colors[0];
  };

  const formatUnlockRequirement = (req: UnlockRequirement) => {
    switch (req.type) {
      case 'CURRENCY':
        return `Requires ${req.value.toLocaleString()} ${req.currency === CurrencyType.CRYPTO_COIN ? 'CTC' : req.currency}`;
      case 'CLICKS':
        return `Requires ${req.value.toLocaleString()} total clicks`;
      case 'RESEARCH':
        return `Requires research: ${req.value}`;
      default:
        return 'Unlock requirement not met';
    }
  };

  return (
    <div className={`bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 backdrop-blur-sm ${!isUnlocked ? 'opacity-75' : ''} ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className={`p-2 rounded-lg ${getTierColor(tier)}`}>
            {isUnlocked ? (
              <CpuChipIcon className="w-5 h-5" />
            ) : (
              <LockClosedIcon className="w-5 h-5" />
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className="font-medium text-white">{name}</h3>
              <div className="flex items-center space-x-1">
                {Array.from({ length: tier }).map((_, i) => (
                  <StarIcon key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                ))}
              </div>
            </div>
            <p className="text-xs text-gray-400">
              {isUnlocked ? `Level ${level}/${maxLevel}` : 'Locked'}
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm font-medium text-white">×{quantity}</div>
          <div className="text-xs text-gray-400">Owned</div>
        </div>
      </div>

      {/* Description */}
      {description && (
        <p className="text-xs text-gray-400 mb-3 leading-relaxed">{description}</p>
      )}

      {/* Unlock Requirement */}
      {!isUnlocked && unlockRequirement && (
        <div className="mb-4 p-2 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
          <div className="flex items-center space-x-2">
            <LockClosedIcon className="w-4 h-4 text-yellow-400" />
            <span className="text-xs text-yellow-300">
              {formatUnlockRequirement(unlockRequirement)}
            </span>
          </div>
        </div>
      )}

      {/* Stats */}
      {isUnlocked && (
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Output per rig:</span>
            <CurrencyDisplay 
              amount={output} 
              currency={CurrencyType.CRYPTO_COIN}
              className="text-xs"
            />
          </div>
          {quantity > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Total output:</span>
              <CurrencyDisplay 
                amount={totalOutput} 
                currency={CurrencyType.CRYPTO_COIN}
                className="text-xs"
              />
            </div>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="space-y-2">
        {/* Purchase button */}
        {isUnlocked && purchaseCost !== undefined && onPurchase && (
          <button
            onClick={onPurchase}
            disabled={!canAffordPurchase}
            className={`w-full py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              canAffordPurchase
                ? 'bg-green-500/20 border border-green-500/50 text-green-300 hover:bg-green-500/30'
                : 'bg-gray-700/50 border border-gray-600/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <div className="flex items-center justify-between">
              <span>Buy +1</span>
              <CurrencyDisplay 
                amount={purchaseCost} 
                currency={CurrencyType.CRYPTO_COIN}
                className="text-xs"
              />
            </div>
          </button>
        )}

        {/* Upgrade button */}
        {isUnlocked && upgradeCost !== undefined && onUpgrade && quantity > 0 && level < maxLevel && (
          <button
            onClick={onUpgrade}
            disabled={!canAffordUpgrade}
            className={`w-full py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              canAffordUpgrade
                ? 'bg-blue-500/20 border border-blue-500/50 text-blue-300 hover:bg-blue-500/30'
                : 'bg-gray-700/50 border border-gray-600/50 text-gray-500 cursor-not-allowed'
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <ArrowUpIcon className="w-4 h-4" />
                <span>Upgrade</span>
              </div>
              <CurrencyDisplay 
                amount={upgradeCost} 
                currency={CurrencyType.CRYPTO_COIN}
                className="text-xs"
              />
            </div>
          </button>
        )}

        {/* Max level indicator */}
        {isUnlocked && quantity > 0 && level >= maxLevel && (
          <div className="w-full py-2 px-3 rounded-lg text-sm font-medium bg-yellow-500/20 border border-yellow-500/50 text-yellow-300 text-center">
            Max Level Reached
          </div>
        )}

        {/* Locked state message */}
        {!isUnlocked && (
          <div className="w-full py-2 px-3 rounded-lg text-sm font-medium bg-gray-700/50 border border-gray-600/50 text-gray-400 text-center">
            <div className="flex items-center justify-center space-x-2">
              <LockClosedIcon className="w-4 h-4" />
              <span>Locked</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}