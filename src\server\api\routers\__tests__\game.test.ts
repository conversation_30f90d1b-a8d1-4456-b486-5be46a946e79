import { describe, test, expect } from 'vitest';
import { RigType } from '~/types/game';
import { 
  calculateTotalCPS,
  calculateRigCost,
  calculateUpgradeCost,
  validateClickRate,
  validateClickPattern
} from '~/lib/game-utils';
import { GAME_CONSTANTS } from '~/config/game-constants';
import type { MiningRig } from '~/types/game';
import { Decimal } from '@prisma/client/runtime/library';

describe('Game Logic Functions', () => {
  const mockMiningRigs: MiningRig[] = [
    {
      id: '1',
      playerId: 'player1',
      rigType: RigType.USB_ASIC,
      level: 1,
      quantity: 5,
      baseOutput: new Decimal(0.1),
      efficiency: new Decimal(1.0),
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      playerId: 'player1',
      rigType: RigType.GPU_FARM,
      level: 2,
      quantity: 2,
      baseOutput: new Decimal(1.0),
      efficiency: new Decimal(1.2),
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  describe('Mining Calculations', () => {
    test('should calculate correct total CPS from mining rigs', () => {
      const totalCPS = calculateTotalCPS(mockMiningRigs);
      
      // USB_ASIC: 0.1 * 1.0 * 1 * 5 = 0.5
      // GPU_FARM: 1.0 * 1.2 * 2 * 2 = 4.8
      // Total: 0.5 + 4.8 = 5.3
      expect(totalCPS).toBe(5.3);
    });

    test('should calculate correct rig purchase cost', () => {
      const cost = calculateRigCost(RigType.USB_ASIC, 0, 3);
      
      // Base cost: 10, multiplier: 1.15
      // First rig: 10 * 1.15^0 = 10
      // Second rig: 10 * 1.15^1 = 11.5
      // Third rig: 10 * 1.15^2 = 13.225
      // Total: 10 + 11.5 + 13.225 = 34.725
      expect(cost).toBeCloseTo(34.725, 2);
    });

    test('should calculate correct upgrade cost', () => {
      const cost = calculateUpgradeCost(RigType.USB_ASIC, 1, 2);
      
      // Base cost: 10, upgrade multiplier: 1.5
      // Level 1 to 2: 10 * 1.5^1 = 15
      // Level 2 to 3: 10 * 1.5^2 = 22.5
      // Total: 15 + 22.5 = 37.5
      expect(cost).toBeCloseTo(37.5, 2);
    });
  });

  describe('Rate Limiting', () => {
    test('should validate normal click rate', () => {
      const now = Date.now();
      const clicks = [
        now - 500,
        now - 400,
        now - 300,
        now - 200,
        now - 100,
      ];
      
      expect(validateClickRate(clicks)).toBe(true);
    });

    test('should reject excessive click rate', () => {
      const now = Date.now();
      const clicks = Array.from({ length: 25 }, (_, i) => now - i * 10); // 25 clicks in 250ms
      
      expect(validateClickRate(clicks)).toBe(false);
    });

    test('should validate clicks at the limit', () => {
      const now = Date.now();
      const clicks = Array.from({ length: 20 }, (_, i) => now - i * 50); // 20 clicks in 1 second
      
      expect(validateClickRate(clicks)).toBe(true);
    });

    test('should handle invalid click data', () => {
      expect(validateClickRate([])).toBe(true);
      expect(validateClickRate([null as any])).toBe(true);
      expect(validateClickRate(['invalid' as any])).toBe(true);
    });
  });

  describe('Click Pattern Validation', () => {
    test('should validate human-like click patterns', () => {
      const now = Date.now();
      const humanClicks = [
        now - 1000,
        now - 850,
        now - 650,
        now - 400,
        now - 150,
      ];
      
      expect(validateClickPattern(humanClicks)).toBe(true);
    });

    test('should reject suspiciously consistent patterns', () => {
      const now = Date.now();
      const botClicks = Array.from({ length: 10 }, (_, i) => now - i * 50); // Exactly 50ms apart
      
      expect(validateClickPattern(botClicks)).toBe(false);
    });

    test('should reject impossibly fast clicking', () => {
      const now = Date.now();
      const tooFastClicks = Array.from({ length: 10 }, (_, i) => now - i * 20); // 20ms apart
      
      expect(validateClickPattern(tooFastClicks)).toBe(false);
    });

    test('should allow fast but varied clicking', () => {
      const now = Date.now();
      const fastButVariedClicks = [
        now - 1000,
        now - 920,
        now - 850,
        now - 750,
        now - 680,
        now - 580,
        now - 500,
      ];
      
      expect(validateClickPattern(fastButVariedClicks)).toBe(true);
    });

    test('should handle insufficient data', () => {
      expect(validateClickPattern([])).toBe(true);
      expect(validateClickPattern([Date.now()])).toBe(true);
      expect(validateClickPattern([Date.now(), Date.now() - 100])).toBe(true);
    });
  });

  describe('Game Constants', () => {
    test('should have proper rate limiting constants', () => {
      expect(GAME_CONSTANTS.CLICK_RATE_LIMIT).toBe(20);
      expect(GAME_CONSTANTS.CLICK_RATE_WINDOW).toBe(1000);
      expect(GAME_CONSTANTS.CLICK_BURST_LIMIT).toBe(50);
    });

    test('should have proper offline production constants', () => {
      expect(GAME_CONSTANTS.MAX_OFFLINE_HOURS).toBe(24);
      expect(GAME_CONSTANTS.OFFLINE_EFFICIENCY_THRESHOLD).toBe(8);
      expect(GAME_CONSTANTS.OFFLINE_EFFICIENCY_PENALTY).toBe(0.8);
    });
  });
});