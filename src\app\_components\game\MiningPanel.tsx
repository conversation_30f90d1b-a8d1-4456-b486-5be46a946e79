"use client";

import { useState } from "react";
import { CpuChipIcon, SparklesIcon } from "@heroicons/react/24/outline";
import { RigCard } from "../ui/RigCard";
import { CurrencyDisplay } from "../ui/CurrencyDisplay";
import { api } from "~/trpc/react";
import { CurrencyType } from "~/types/game";
import { useGameData } from "~/stores/game-store";
import { useClickBatcher } from "~/hooks/useClickBatcher";
import { useSmartPolling } from "~/hooks/useSmartPolling";
import { CURRENCY_CONFIGS } from "~/config/game-constants";

export function MiningPanel() {
  const [clickEffect, setClickEffect] = useState(false);

  // Use optimized game data management
  const gameData = useGameData();
  const { addClick, isPending, pendingClicks, connected, error } = useClickBatcher();

  // Smart polling - only when needed
  useSmartPolling(gameData.refetch, {
    interval: 10000, // 10 seconds
    pauseOnActivity: true,
    pauseOnHidden: true,
  });

  // Fetch available rigs with tier and unlock information
  const { data: availableRigs, refetch: refetchRigs } = api.game.getAvailableRigs.useQuery();

  const purchaseRig = api.game.purchaseRig.useMutation({
    onSuccess: () => {
      gameData.refetch();
      refetchRigs();
    },
  });

  const upgradeRig = api.game.upgradeRig.useMutation({
    onSuccess: () => {
      gameData.refetch();
      refetchRigs();
    },
  });

  const handleClick = () => {
    if (!isPending && connected) {
      const success = addClick();
      if (success) {
        setClickEffect(true);
        setTimeout(() => setClickEffect(false), 200);
      }
    }
  };

  const handlePurchaseRig = (rigType: string) => {
    purchaseRig.mutate({ rigType: rigType as any, quantity: 1 });
  };

  const handleUpgradeRig = (rigId: string) => {
    upgradeRig.mutate({ rigId, levels: 1 });
  };

  if (!gameData.player || !availableRigs) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-400">Loading mining panel...</div>
      </div>
    );
  }

  const currentBalance = gameData.getCurrentBalance();

  return (
    <div className="space-y-6">
      {/* Mining Click Section */}
      <div className="bg-gradient-to-br from-purple-500/20 to-blue-500/20 border border-purple-500/30 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold text-white mb-4">
          Mine {CURRENCY_CONFIGS[gameData.getActiveMiningCurrency()]?.name || 'CryptoCoin'}
        </h2>

        {/* Click Button */}
        <div className="mb-6">
          <button
            onClick={handleClick}
            className={`
              relative w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-orange-500 to-yellow-500
              hover:from-orange-400 hover:to-yellow-400 active:scale-95
              transition-all duration-200 shadow-lg hover:shadow-xl
              ${isPending ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
          >
            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/20 to-transparent" />
            <CpuChipIcon className="w-16 h-16 text-white mx-auto" />
            {clickEffect && (
              <div className="absolute inset-0 rounded-full border-4 border-yellow-300 animate-ping" />
            )}
          </button>

          <div className="mt-4 space-y-2">
            <div className="text-lg font-medium text-white">
              Click to mine CTC!
            </div>
            <div className="text-sm text-gray-300">
              Pending clicks: {pendingClicks}
            </div>
          </div>
        </div>

        {/* Connection Status & Balance */}
        <div className="flex items-center justify-center gap-4 flex-wrap">
          <div className="bg-gray-800/50 rounded-lg p-4">
            <div className="text-sm text-gray-400 mb-1">Current Balance</div>
            <CurrencyDisplay
              amount={currentBalance}
              currency={gameData.getActiveMiningCurrency()}
              showIcon
              className="text-xl"
            />
          </div>

          {/* WebSocket Status */}
          <div className="bg-gray-800/50 rounded-lg p-4">
            <div className="text-sm text-gray-400 mb-1">Connection</div>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${connected ? 'bg-green-400' : 'bg-red-400'}`} />
              <span className={`text-sm font-medium ${connected ? 'text-green-400' : 'text-red-400'}`}>
                {connected ? 'Online' : 'Offline'}
              </span>
            </div>
            {error && (
              <div className="text-xs text-red-400 mt-1">{error}</div>
            )}
          </div>
        </div>
      </div>

      {/* Mining Rigs Section */}
      <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-6 backdrop-blur-sm">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Mining Rigs</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <SparklesIcon className="w-4 h-4" />
            <span>Automate your mining</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {availableRigs.map((rigData) => {
            // Find existing rig of this type from gameData
            const existingRig = gameData.miningRigs.find(
              rig => rig.rigType === rigData.type
            );

            return (
              <RigCard
                key={rigData.type}
                name={rigData.name}
                description={rigData.description}
                tier={rigData.tier}
                level={rigData.currentLevel}
                quantity={rigData.currentQuantity}
                maxLevel={rigData.maxLevel}
                output={rigData.currentQuantity > 0 ? rigData.baseOutput * rigData.currentLevel : rigData.baseOutput}
                totalOutput={rigData.totalOutput}
                purchaseCost={rigData.nextPurchaseCost}
                upgradeCost={rigData.nextUpgradeCost ?? undefined}
                isUnlocked={rigData.isUnlocked}
                unlockRequirement={rigData.unlockRequirement}
                onPurchase={rigData.isUnlocked ? () => handlePurchaseRig(rigData.type) : undefined}
                onUpgrade={existingRig && rigData.nextUpgradeCost ? () => handleUpgradeRig(existingRig.id) : undefined}
                canAffordPurchase={rigData.isUnlocked && currentBalance >= rigData.nextPurchaseCost}
                canAffordUpgrade={rigData.nextUpgradeCost ? currentBalance >= rigData.nextUpgradeCost : false}
              />
            );
          })}
        </div>

        {/* Mining Stats */}
        {gameData.miningRigs.length > 0 && (
          <div className="mt-6 p-4 bg-gray-700/30 rounded-lg">
            <h3 className="font-medium text-white mb-3">Mining Statistics</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-gray-400">Total Rigs</div>
                <div className="text-white font-medium">
                  {gameData.miningRigs.reduce((sum, rig) => sum + rig.quantity, 0)}
                </div>
              </div>
              <div>
                <div className="text-gray-400">Total Output</div>
                <CurrencyDisplay
                  amount={gameData.getTotalCPS()}
                  currency={CurrencyType.CRYPTO_COIN}
                  className="text-white font-medium"
                />
              </div>
              <div>
                <div className="text-gray-400">Efficiency</div>
                <div className="text-white font-medium">
                  {(gameData.miningRigs.reduce((sum, rig) =>
                    sum + rig.efficiency, 0
                  ) / Math.max(1, gameData.miningRigs.length) * 100).toFixed(1)}%
                </div>
              </div>
              <div>
                <div className="text-gray-400">Total Clicks</div>
                <div className="text-white font-medium">
                  {gameData.player?.stats?.totalClicks?.toLocaleString() || '0'}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}