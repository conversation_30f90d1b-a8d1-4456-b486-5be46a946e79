import type { NextApiRequest, NextApiResponse } from 'next';
import { generateWebSocketToken } from '~/lib/websocket-auth';
import { db } from '~/server/db';

interface TokenResponse {
  token?: string;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<TokenResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔐 WebSocket token request received');
    console.log('🔐 Request headers:', Object.keys(req.headers));
    console.log('🔐 Cookies:', req.headers.cookie ? 'Present' : 'Missing');

    // Try to get session token from cookies
    const cookies = req.headers.cookie;
    if (!cookies) {
      console.log('🚫 No cookies found in request');
      return res.status(401).json({ error: 'No session found' });
    }

    // Try different cookie patterns for NextAuth v5
    const patterns = [
      /next-auth\.session-token=([^;]+)/,
      /authjs\.session-token=([^;]+)/,
      /__Secure-next-auth\.session-token=([^;]+)/,
      /__Secure-authjs\.session-token=([^;]+)/,
      /next-auth\.session-token\.0=([^;]+)/, // Chunked cookies
      /authjs\.session-token\.0=([^;]+)/,
    ];

    let sessionToken = null;
    let usedPattern = '';
    for (const pattern of patterns) {
      const match = cookies.match(pattern);
      if (match) {
        sessionToken = match[1];
        usedPattern = pattern.source;
        console.log('🔐 Found session token with pattern:', usedPattern);
        break;
      }
    }

    if (!sessionToken) {
      console.log('🚫 No session token found in cookies');
      console.log('🚫 Available cookies:', cookies.split(';').map(c => c.trim().split('=')[0]));
      console.log('🚫 Full cookie string (first 200 chars):', cookies.substring(0, 200));
      return res.status(401).json({ error: 'No session token' });
    }

    console.log('🔐 Found session token (first 20 chars):', sessionToken.substring(0, 20) + '...');
    console.log('🔐 Looking up session in database...');

    // Look up the session in the database
    const session = await db.session.findUnique({
      where: { sessionToken },
      include: { user: true }
    });

    if (!session) {
      console.log('🚫 Session not found in database');
      console.log('🚫 Searched for token:', sessionToken.substring(0, 20) + '...');
      return res.status(401).json({ error: 'Session not found' });
    }

    if (!session.user) {
      console.log('🚫 Session found but no user associated');
      return res.status(401).json({ error: 'No user in session' });
    }

    // Check if session is expired
    if (session.expires < new Date()) {
      console.log('🚫 Session has expired');
      console.log('🚫 Expired at:', session.expires);
      console.log('🚫 Current time:', new Date());
      return res.status(401).json({ error: 'Session expired' });
    }

    console.log('✅ Valid session found for user:', session.user.id);

    // Generate WebSocket token
    const token = generateWebSocketToken(session.user.id);
    console.log('✅ WebSocket token generated successfully');

    res.status(200).json({ token });
  } catch (error) {
    console.error('❌ Failed to generate WebSocket token:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
