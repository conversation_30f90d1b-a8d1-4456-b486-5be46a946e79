"use client";

import { useState } from "react";
import { CurrencyType } from "~/types/game";
import { useGameData } from "~/stores/game-store";
import { ArrowUpIcon, ArrowDownIcon, MinusIcon } from "@heroicons/react/24/solid";
import { formatCurrencyWithSymbol } from "~/lib/game-utils";

export function MarketOverview() {
  const [showAll, setShowAll] = useState(false);
  const gameData = useGameData();
  const { marketData } = gameData;

  // Filter currencies to show only unlocked ones, or all if showAll is true
  const currencies = Object.entries(marketData.currencies || {})
    .filter(([_, data]) => showAll || data.isUnlocked)
    .sort((a, b) => {
      // Sort by unlock status first, then by name
      if (a[1].isUnlocked !== b[1].isUnlocked) return a[1].isUnlocked ? -1 : 1;
      return a[1].name.localeCompare(b[1].name);
    });

  if (!currencies.length) {
    return (
      <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4">
        <div className="text-gray-400 text-center">Market data loading...</div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-white">Crypto Market</h3>
        <button
          onClick={() => setShowAll(!showAll)}
          className="text-xs text-purple-400 hover:text-purple-300"
        >
          {showAll ? "Show Unlocked Only" : "Show All"}
        </button>
      </div>

      <div className="space-y-1">
        {currencies.map(([currencyType, data]) => {
          const change = data.change24h * 100; // Convert to percentage
          const isPositive = change > 0;
          const isNeutral = Math.abs(change) < 0.1;
          
          return (
            <div 
              key={currencyType}
              className={`flex items-center justify-between p-2 rounded ${
                !data.isUnlocked ? 'opacity-50' : ''
              }`}
            >
              <div className="flex items-center space-x-2">
                <div 
                  className="w-4 h-4 rounded-full" 
                  style={{ backgroundColor: data.color }}
                ></div>
                <span className="text-white">{data.symbol}</span>
                {!data.isUnlocked && (
                  <span className="text-xs text-gray-500">(Locked)</span>
                )}
              </div>
              
              <div className="flex items-center space-x-4">
                <span className="text-white font-medium">
                  ${data.price.toFixed(2)}
                </span>
                <div className={`flex items-center space-x-1 ${
                  isPositive ? 'text-green-400' : isNeutral ? 'text-gray-400' : 'text-red-400'
                }`}>
                  {isPositive ? (
                    <ArrowUpIcon className="w-3 h-3" />
                  ) : isNeutral ? (
                    <MinusIcon className="w-3 h-3" />
                  ) : (
                    <ArrowDownIcon className="w-3 h-3" />
                  )}
                  <span className="text-xs">
                    {Math.abs(change).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {marketData.trends && marketData.trends.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <h4 className="text-sm font-medium text-gray-300 mb-2">Market Trends</h4>
          <div className="space-y-1">
            {marketData.trends.slice(0, 3).map((trend, index) => (
              <div key={index} className="text-xs text-gray-400">
                {trend.currency} is trending {trend.direction.toLowerCase()} 
                {trend.strength > 0.7 ? ' strongly' : trend.strength > 0.3 ? ' moderately' : ''}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}