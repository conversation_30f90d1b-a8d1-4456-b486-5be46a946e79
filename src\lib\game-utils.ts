import type { <PERSON><PERSON><PERSON>, <PERSON>S<PERSON>, ShardEffect } from "../types/game";
import { CurrencyType } from "../types/game";
import { RIG_CONFIGS, GAME_CONSTANTS } from "../config/game-constants";

/**
 * Calculate the total CTC per second from all mining rigs
 */
export function calculateTotalCPS(rigs: MiningRig[]): number {
  return rigs.reduce((total, rig) => {
    const rigConfig = RIG_CONFIGS[rig.rigType];
    const baseOutput = typeof rig.baseOutput === 'object' && 'toNumber' in rig.baseOutput 
      ? rig.baseOutput.toNumber() 
      : Number(rig.baseOutput);
    const efficiency = typeof rig.efficiency === 'object' && 'toNumber' in rig.efficiency 
      ? rig.efficiency.toNumber() 
      : Number(rig.efficiency);
    
    const rigOutput = baseOutput * efficiency * rig.level * rig.quantity;
    return total + rigOutput;
  }, 0);
}

/**
 * Calculate the cost of purchasing a mining rig
 */
export function calculateRigCost(rigType: string, currentQuantity: number, purchaseQuantity: number): number {
  const config = RIG_CONFIGS[rigType as keyof typeof RIG_CONFIGS];
  if (!config) return 0;

  let totalCost = 0;
  for (let i = 0; i < purchaseQuantity; i++) {
    const cost = config.baseCost * Math.pow(config.costMultiplier, currentQuantity + i);
    totalCost += cost;
  }
  
  return totalCost;
}

/**
 * Calculate the cost of upgrading a mining rig
 */
export function calculateUpgradeCost(rigType: string, currentLevel: number, upgradeLevels: number): number {
  const config = RIG_CONFIGS[rigType as keyof typeof RIG_CONFIGS];
  if (!config) return 0;

  let totalCost = 0;
  for (let i = 0; i < upgradeLevels; i++) {
    const levelCost = config.baseCost * Math.pow(config.upgradeCostMultiplier, currentLevel + i);
    totalCost += levelCost;
  }
  
  return totalCost;
}

/**
 * Apply shard bonuses to a base value
 */
export function applyShardBonuses(baseValue: number, shards: PlayerShard[], target: string): number {
  let result = baseValue;
  let multipliers = 1;
  
  for (const playerShard of shards) {
    if (!playerShard.equipped || !playerShard.shard) continue;
    
    const effects = playerShard.shard.effects as ShardEffect[];
    for (const effect of effects) {
      if (effect.target === target || effect.target === 'all_production' || effect.target === 'everything') {
        switch (effect.type) {
          case 'FLAT_BONUS':
            result += effect.value * playerShard.quantity;
            break;
          case 'MULTIPLIER':
            multipliers *= Math.pow(effect.value, playerShard.quantity);
            break;
          case 'SPECIAL_ABILITY':
            // Special abilities are handled separately
            break;
        }
      }
    }
  }
  
  return result * multipliers;
}

/**
 * Calculate offline production based on time away and mining rigs
 */
export function calculateOfflineProduction(
  rigs: MiningRig[], 
  shards: PlayerShard[], 
  offlineTimeSeconds: number
): number {
  const offlineHours = offlineTimeSeconds / 3600;
  const maxOfflineHours = GAME_CONSTANTS.MAX_OFFLINE_HOURS;
  const effectiveHours = Math.min(offlineHours, maxOfflineHours);
  
  // Apply efficiency penalty for long offline periods
  let efficiency = 1.0;
  if (effectiveHours > GAME_CONSTANTS.OFFLINE_EFFICIENCY_THRESHOLD) {
    efficiency = GAME_CONSTANTS.OFFLINE_EFFICIENCY_PENALTY;
  }
  
  const baseCPS = calculateTotalCPS(rigs);
  const bonusedCPS = applyShardBonuses(baseCPS, shards, 'offline_production');
  
  return bonusedCPS * effectiveHours * 3600 * efficiency;
}

/**
 * Calculate click power based on player stats and bonuses
 */
export function calculateClickPower(shards: PlayerShard[], baseClickPower: number = GAME_CONSTANTS.BASE_CLICK_POWER): number {
  return applyShardBonuses(baseClickPower, shards, 'click_power');
}

/**
 * Determine if a shard should drop based on rarity and action count
 */
export function shouldDropShard(actionCount: number, rarity: string): boolean {
  const dropRate = GAME_CONSTANTS.SHARD_DROP_RATES[rarity as keyof typeof GAME_CONSTANTS.SHARD_DROP_RATES];
  if (!dropRate) return false;
  
  // Convert drop rate from per 1000 to probability
  const probability = dropRate / 1000;
  return Math.random() < probability;
}

/**
 * Calculate prestige requirements and rewards
 */
export function calculatePrestigeReward(totalCTC: number, currentPrestigeLevel: number): number {
  const requirement = GAME_CONSTANTS.PRESTIGE_BASE_REQUIREMENT * Math.pow(GAME_CONSTANTS.PRESTIGE_MULTIPLIER, currentPrestigeLevel);
  
  if (totalCTC < requirement) return 0;
  
  const basePoints = GAME_CONSTANTS.BLOCKCHAIN_POINTS_BASE;
  const levelMultiplier = Math.pow(1.5, currentPrestigeLevel);
  const progressMultiplier = Math.floor(totalCTC / requirement);
  
  return Math.floor(basePoints * levelMultiplier * progressMultiplier);
}

/**
 * Format large numbers with appropriate suffixes
 */
export function formatNumber(num: number): string {
  if (num >= 1e12) return `${(num / 1e12).toFixed(2)}T`;
  if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
  if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
  if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
  return num.toFixed(2);
}

/**
 * Convert Decimal-like values to number safely (client-safe version)
 */
export function decimalToNumber(decimal: any): number {
  if (typeof decimal === 'number') return decimal;
  if (typeof decimal === 'string') return parseFloat(decimal);
  if (typeof decimal === 'object' && decimal !== null && 'toNumber' in decimal) {
    return decimal.toNumber();
  }
  return 0;
}

/**
 * Validate click rate to prevent cheating
 */
export function validateClickRate(clicks: number[], timeWindow: number = GAME_CONSTANTS.CLICK_RATE_WINDOW): boolean {
  if (!Array.isArray(clicks)) return false;
  
  const now = Date.now();
  const recentClicks = clicks.filter(clickTime => 
    typeof clickTime === 'number' && 
    clickTime > 0 && 
    now - clickTime < timeWindow
  );
  
  return recentClicks.length <= GAME_CONSTANTS.CLICK_RATE_LIMIT;
}

/**
 * Enhanced validation for click timing patterns to detect automated clicking
 */
export function validateClickPattern(clicks: number[]): boolean {
  if (clicks.length < 3) return true; // Not enough data to analyze pattern
  
  const intervals: number[] = [];
  for (let i = 1; i < clicks.length; i++) {
    intervals.push(clicks[i]! - clicks[i - 1]!);
  }
  
  // Check for suspiciously consistent intervals (likely bot behavior)
  const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
  const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
  const standardDeviation = Math.sqrt(variance);
  
  // If standard deviation is too low, clicks are too consistent (likely automated)
  const consistencyThreshold = 10; // milliseconds
  if (standardDeviation < consistencyThreshold && avgInterval < 100) {
    return false;
  }
  
  // Check for impossible click speeds (faster than humanly possible)
  const minHumanInterval = 50; // 50ms minimum between clicks
  const tooFastClicks = intervals.filter(interval => interval < minHumanInterval).length;
  const tooFastRatio = tooFastClicks / intervals.length;
  
  if (tooFastRatio > 0.3) { // More than 30% of clicks are too fast
    return false;
  }
  
  return true;
}

/**
 * Calculate market price with volatility
 */
export function calculateMarketPrice(basePrice: number, volatility: number, timeElapsed: number): number {
  // Simple random walk with mean reversion
  const randomFactor = (Math.random() - 0.5) * 2; // -1 to 1
  const volatilityFactor = volatility * randomFactor * Math.sqrt(timeElapsed / 1000);
  const meanReversion = 0.01 * (1 - basePrice / basePrice); // Slight mean reversion
  
  const priceChange = volatilityFactor + meanReversion;
  const maxChange = GAME_CONSTANTS.MAX_PRICE_CHANGE;
  const clampedChange = Math.max(-maxChange, Math.min(maxChange, priceChange));
  
  return basePrice * (1 + clampedChange);
}

/**
 * Calculate research cost based on level and type
 */
export function calculateResearchCost(researchId: string, currentLevel: number): number {
  const baseCost = GAME_CONSTANTS.RESEARCH_BASE_COST;
  const multiplier = GAME_CONSTANTS.RESEARCH_COST_MULTIPLIER;
  
  return baseCost * Math.pow(multiplier, currentLevel);
}

/**
 * Calculate data center establishment cost
 */
export function calculateDataCenterCost(locationId: string, playerDataCenters: number): number {
  const baseCost = GAME_CONSTANTS.LOCATION_BASE_COST;
  const multiplier = GAME_CONSTANTS.LOCATION_COST_MULTIPLIER;
  
  return baseCost * Math.pow(multiplier, playerDataCenters);
}

/**
 * Generate a random shard based on rarity weights
 */
export function generateRandomShard(availableShards: any[]): any | null {
  if (availableShards.length === 0) return null;
  
  // Weight shards by inverse rarity (common shards more likely)
  const rarityWeights = {
    COMMON: 50,
    UNCOMMON: 25,
    RARE: 15,
    EPIC: 7,
    LEGENDARY: 2,
    MYTHIC: 1,
  };
  
  const weightedShards = availableShards.map(shard => ({
    ...shard,
    weight: rarityWeights[shard.rarity as keyof typeof rarityWeights] || 1,
  }));
  
  const totalWeight = weightedShards.reduce((sum, shard) => sum + shard.weight, 0);
  let random = Math.random() * totalWeight;
  
  for (const shard of weightedShards) {
    random -= shard.weight;
    if (random <= 0) {
      return shard;
    }
  }
  
  return weightedShards[0]; // Fallback
}



/**
 * Calculate the total output of a specific rig including level bonuses
 */
export function calculateRigOutput(rig: MiningRig): number {
  const baseOutput = typeof rig.baseOutput === 'object' && 'toNumber' in rig.baseOutput 
    ? rig.baseOutput.toNumber() 
    : Number(rig.baseOutput);
  const efficiency = typeof rig.efficiency === 'object' && 'toNumber' in rig.efficiency 
    ? rig.efficiency.toNumber() 
    : Number(rig.efficiency);
  
  return baseOutput * efficiency * rig.level * rig.quantity;
}

/**
 * Get rig tier information for UI display
 */
export function getRigTierInfo(rigType: string): {
  tier: number;
  maxLevel: number;
  unlockRequirement: any;
} | null {
  const config = RIG_CONFIGS[rigType as keyof typeof RIG_CONFIGS];
  if (!config) return null;

  return {
    tier: config.tier,
    maxLevel: config.maxLevel,
    unlockRequirement: config.unlockRequirement,
  };
}

/**
 * Calculate the next upgrade cost for a rig
 */
export function getNextUpgradeCost(rigType: string, currentLevel: number): number {
  return calculateUpgradeCost(rigType, currentLevel, 1);
}

/**
 * Calculate the next purchase cost for a rig
 */
export function getNextPurchaseCost(rigType: string, currentQuantity: number): number {
  return calculateRigCost(rigType, currentQuantity, 1);
}

/**
 * Get currency configuration by type
 */
export function getCurrencyConfig(currencyType: CurrencyType) {
  return CURRENCY_CONFIGS[currencyType];
}

/**
 * Convert amount from one currency to another based on current market rates
 */
export function convertCurrency(
  amount: number,
  fromCurrency: CurrencyType,
  toCurrency: CurrencyType,
  marketData: { [key in CurrencyType]?: { price: number } }
): number {
  if (fromCurrency === toCurrency) return amount;
  
  const fromPrice = marketData[fromCurrency]?.price ?? CURRENCY_CONFIGS[fromCurrency].baseValue;
  const toPrice = marketData[toCurrency]?.price ?? CURRENCY_CONFIGS[toCurrency].baseValue;
  
  return amount * (fromPrice / toPrice);
}

/**
 * Simulate market price changes for all currencies
 */
export function simulateMarketPrices(
  currentPrices: { [key in CurrencyType]?: { price: number; volatility: number } },
  timeElapsedMs: number
): { [key in CurrencyType]?: { price: number; change: number } } {
  const result: { [key in CurrencyType]?: { price: number; change: number } } = {};
  
  for (const [currency, data] of Object.entries(currentPrices)) {
    if (!data) continue;
    
    const currencyType = currency as CurrencyType;
    const config = CURRENCY_CONFIGS[currencyType];
    const volatility = data.volatility || config.volatility || GAME_CONSTANTS.BASE_VOLATILITY;
    
    const newPrice = calculateMarketPrice(data.price, volatility, timeElapsedMs);
    const change = newPrice - data.price;
    
    result[currencyType] = {
      price: newPrice,
      change: change,
    };
  }
  
  return result;
}



/**
 * Calculate mining output for a specific currency
 */
export function calculateCurrencyMiningOutput(
  baseCPS: number,
  currencyType: CurrencyType,
  shards: PlayerShard[] = []
): number {
  const config = CURRENCY_CONFIGS[currencyType];
  if (!config) return 0;
  
  // Apply currency-specific mining efficiency
  let output = baseCPS * (config.miningEfficiency || 1.0);
  
  // Apply currency-specific shard bonuses
  output = applyShardBonuses(output, shards, `mining_${currencyType.toLowerCase()}`);
  
  return output;
}

/**
 * Format currency amount with appropriate symbol
 */
export function formatCurrencyWithSymbol(amount: number, currencyType: CurrencyType): string {
  const config = CURRENCY_CONFIGS[currencyType];
  if (!config) return `${amount.toFixed(2)} ???`;
  
  if (amount >= 1e12) return `${(amount / 1e12).toFixed(2)}T ${config.symbol}`;
  if (amount >= 1e9) return `${(amount / 1e9).toFixed(2)}B ${config.symbol}`;
  if (amount >= 1e6) return `${(amount / 1e6).toFixed(2)}M ${config.symbol}`;
  if (amount >= 1e3) return `${(amount / 1e3).toFixed(2)}K ${config.symbol}`;
  return `${amount.toFixed(2)} ${config.symbol}`;
}