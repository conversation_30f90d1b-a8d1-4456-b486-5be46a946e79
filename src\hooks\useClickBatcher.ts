import { useCallback, useEffect, useState } from 'react';
import { useGameStore } from '~/stores/game-store';
import { useGameWebSocketContext } from '~/app/_components/providers/GameWebSocketProvider';
import { GAME_CONSTANTS } from '~/config/game-constants';

export function useClickBatcher() {
  const [pendingClicks, setPendingClicks] = useState(0);
  const [lastClickTime, setLastClickTime] = useState(0);

  // Get stable references to store methods and data
  const addOptimisticClick = useGameStore(state => state.addOptimisticClick);
  const player = useGameStore(state => state.player);

  // Use the site-wide WebSocket connection
  const { connected, sendClick, error } = useGameWebSocketContext();

  // Listen for click acknowledgments to update pending clicks count
  useEffect(() => {
    const handleClickAcknowledged = (event: CustomEvent) => {
      // Reduce pending clicks when server acknowledges
      setPendingClicks(prev => Math.max(0, prev - 1));
    };

    const handleError = (event: CustomEvent) => {
      console.error('🚨 WebSocket error:', event.detail.message);
      // Reset optimistic state on error
      setPendingClicks(0);
    };

    window.addEventListener('click_acknowledged', handleClickAcknowledged as EventListener);
    window.addEventListener('game_error', handleError as EventListener);

    return () => {
      window.removeEventListener('click_acknowledged', handleClickAcknowledged as EventListener);
      window.removeEventListener('game_error', handleError as EventListener);
    };
  }, []);

  const addClick = useCallback(() => {
    const now = Date.now();

    // Client-side rate limiting (100ms minimum)
    if (now - lastClickTime < 100) {
      console.warn('⚠️ Click too fast, ignoring');
      return false;
    }

    if (!connected) {
      console.warn('⚠️ WebSocket not connected, cannot send click');
      return false;
    }

    // Send click via WebSocket
    const success = sendClick(now);

    if (success) {
      // Add optimistic click to store
      addOptimisticClick(GAME_CONSTANTS.BASE_CLICK_POWER);
      setPendingClicks(prev => prev + 1);
      setLastClickTime(now);
      return true;
    }

    return false;
  }, [connected, sendClick, addOptimisticClick, lastClickTime]);

  return {
    addClick,
    isPending: pendingClicks > 0,
    pendingClicks,
    connected,
    error,
  };
}