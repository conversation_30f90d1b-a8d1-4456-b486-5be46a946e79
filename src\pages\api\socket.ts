import type { NextApiRequest, NextApiResponse } from 'next';
import { initializeGameWebSocket } from '~/server/websocket/game-handler';
import type { Server as HTTPServer } from 'http';
import type { Socket as NetSocket } from 'net';
import { initializeBackgroundJobs } from '~/lib/init-background-jobs';

interface SocketServer extends HTTPServer {
  io?: any;
}

interface SocketWithIO extends NetSocket {
  server: SocketServer;
}

interface NextApiResponseWithSocket extends NextApiResponse {
  socket: SocketWithIO;
}

export default function handler(req: NextApiRequest, res: NextApiResponseWithSocket) {
  console.log('🔧 Socket API route called');

  if (res.socket.server.io) {
    console.log('✅ Socket.IO server already running');
    res.status(200).json({ status: 'already_running' });
    return;
  }

  console.log('🚀 Starting Socket.IO server...');
  initializeBackgroundJobs();

  try {
    // Initialize the proper game WebSocket handler
    const gameHandler = initializeGameWebSocket(res.socket.server);

    // Store the Socket.IO server instance (not the game handler)
    res.socket.server.io = true; // Just mark as initialized

    console.log('✅ Game WebSocket server initialized successfully');
    res.status(200).json({ status: 'initialized' });
  } catch (error) {
    console.error('❌ Failed to initialize WebSocket server:', error);
    res.status(500).json({
      error: 'Failed to initialize WebSocket server',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
    return;
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
}