import { BeakerIcon } from "@heroicons/react/24/outline";
import { GameLayout } from "~/app/_components/layout/GameLayout";
import { PlaceholderPage } from "~/app/_components/ui/PlaceholderPage";
import { auth } from "~/server/auth";
import { HydrateClient } from "~/trpc/server";
import { redirect } from "next/navigation";

export default async function ResearchPage() {
	const session = await auth();

	if (!session?.user) {
		redirect("/api/auth/signin");
	}

	return (
		<HydrateClient>
			<GameLayout>
				<PlaceholderPage
					title="Research & Development"
					description="Invest in cutting-edge research to unlock new technologies, mining capabilities, and advanced features."
					icon={BeakerIcon}
				/>
			</GameLayout>
		</HydrateClient>
	);
}