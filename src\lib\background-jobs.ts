import { db } from "~/server/db";
import { calculateOfflineProduction, calculateTotalCPS, applyShardBonuses, calculateMarketPrice } from "./game-utils";
import { getGameWebSocketHandler } from "~/server/websocket/game-handler";
import { CurrencyType } from "~/types/game";
import { CURRENCY_CONFIGS } from "~/config/game-constants";

/**
 * Background job to update offline production for all players
 * This would typically run on a schedule (e.g., every 5 minutes)
 */
export async function processOfflineProduction() {
  const startTime = Date.now();
  let processedCount = 0;
  let errorCount = 0;
  
  try {
    // Get all players who have been offline for more than 1 minute
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000);
    
    const playersToUpdate = await db.player.findMany({
      where: {
        lastActiveAt: {
          lt: oneMinuteAgo
        },
        miningRigs: {
          some: {} // Only players with mining rigs
        }
      },
      include: {
        miningRigs: true,
        shards: {
          where: { equipped: true },
          include: { shard: true }
        }
      },
      take: 100 // Process in batches
    });

    console.log(`Processing offline production for ${playersToUpdate.length} players`);

    // Process players in parallel batches for better performance
    const batchSize = 10;
    for (let i = 0; i < playersToUpdate.length; i += batchSize) {
      const batch = playersToUpdate.slice(i, i + batchSize);
      
      await Promise.allSettled(
        batch.map(async (player) => {
          try {
            const now = new Date();
            const offlineTimeSeconds = Math.floor((now.getTime() - player.lastActiveAt.getTime()) / 1000);
            
            // Skip if offline time is less than 1 minute
            if (offlineTimeSeconds < 60) return;

            const totalProduction = calculateOfflineProduction(
              player.miningRigs as any, // Type conversion for Prisma types
              player.shards as any,
              offlineTimeSeconds
            );

            if (totalProduction > 0) {
              const updatedPlayer = await db.player.update({
                where: { id: player.id },
                data: {
                  cryptoCoin: {
                    increment: totalProduction
                  },
                  lastActiveAt: now,
                }
              });

              console.log(`Updated player ${player.id} with ${totalProduction.toFixed(2)} CTC from offline production`);

              // Broadcast the balance update to connected clients
              const gameHandler = getGameWebSocketHandler();
              if (gameHandler) {
                const newBalance = typeof updatedPlayer.cryptoCoin === 'object' && 'toNumber' in updatedPlayer.cryptoCoin
                  ? updatedPlayer.cryptoCoin.toNumber()
                  : Number(updatedPlayer.cryptoCoin);

                gameHandler.broadcastGameUpdate(player.userId, {
                  balance: newBalance,
                  cps: calculateTotalCPS(player.miningRigs as any),
                });
              }

              processedCount++;
            }
          } catch (error) {
            console.error(`Error processing player ${player.id}:`, error);
            errorCount++;
          }
        })
      );
    }

    const duration = Date.now() - startTime;
    console.log(`Offline production processing completed: ${processedCount} players updated, ${errorCount} errors, ${duration}ms`);

    return { 
      success: true, 
      playersProcessed: processedCount,
      errors: errorCount,
      duration 
    };
  } catch (error) {
    console.error('Error processing offline production:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error',
      playersProcessed: processedCount,
      errors: errorCount
    };
  }
}

/**
 * Background job to clean up old rate limiting data
 * In production, this would clean up Redis keys instead of in-memory maps
 */
export async function cleanupRateLimitData() {
  try {
    // In production, this would clean up Redis keys
    // For now, we'll just log that cleanup would happen
    console.log('Rate limit data cleanup completed (would clean Redis keys in production)');
    return { success: true, cleanedEntries: 0 };
  } catch (error) {
    console.error('Error cleaning up rate limit data:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Process real-time passive income for online players
 * This runs more frequently to provide smooth passive income updates
 */
export async function processOnlinePassiveIncome() {
  const startTime = Date.now();
  let processedCount = 0;

  try {
    const gameHandler = getGameWebSocketHandler();
    if (!gameHandler) {
      console.log('⚠️ No WebSocket handler available for online passive income');
      return { success: false, message: 'No WebSocket handler' };
    }

    const authenticatedUsers = gameHandler.getAuthenticatedUsers();
    if (authenticatedUsers.length === 0) {
      return { success: true, message: 'No online players with mining rigs' };
    }

    // Get players who are currently online and have mining rigs
    const onlinePlayers = await db.player.findMany({
      where: {
        userId: {
          in: authenticatedUsers
        },
        miningRigs: {
          some: {} // Only players with mining rigs
        }
      },
      include: {
        miningRigs: true,
        shards: {
          where: { equipped: true },
          include: { shard: true }
        }
      },
    });

    if (onlinePlayers.length === 0) {
      return { success: true, message: 'No online players with mining rigs' };
    }

    console.log(`Processing online passive income for ${onlinePlayers.length} authenticated players`);

    // Process each online player
    for (const player of onlinePlayers) {
      try {
        const now = new Date();
        const timeSinceLastUpdate = Math.floor((now.getTime() - player.lastActiveAt.getTime()) / 1000);

        // Only process if it's been at least 30 seconds since last update
        // This gives the client-side simulation time to work without interference
        if (timeSinceLastUpdate < 30) continue;

        const baseCPS = calculateTotalCPS(player.miningRigs as any);
        const bonusedCPS = applyShardBonuses(baseCPS, player.shards as any, 'mining_output');

        if (bonusedCPS > 0) {
          // Calculate passive earnings for a consistent 60-second interval
          // This ensures predictable income regardless of when the job runs
          const PASSIVE_INCOME_INTERVAL = 60; // seconds
          const passiveEarnings = bonusedCPS * PASSIVE_INCOME_INTERVAL;

          console.log(`💰 Passive income for ${player.userId}:`);
          console.log(`   - Mining rigs: ${player.miningRigs.length}`);
          console.log(`   - Base CPS: ${baseCPS.toFixed(4)}`);
          console.log(`   - Bonused CPS: ${bonusedCPS.toFixed(4)}`);
          console.log(`   - Interval: ${PASSIVE_INCOME_INTERVAL}s`);
          console.log(`   - Earnings: ${passiveEarnings.toFixed(4)} CTC`);

          const updatedPlayer = await db.player.update({
            where: { id: player.id },
            data: {
              cryptoCoin: {
                increment: passiveEarnings
              },
              lastActiveAt: now,
            }
          });

          // Broadcast the update to the connected client
          const newBalance = typeof updatedPlayer.cryptoCoin === 'object' && 'toNumber' in updatedPlayer.cryptoCoin
            ? updatedPlayer.cryptoCoin.toNumber()
            : Number(updatedPlayer.cryptoCoin);

          gameHandler.broadcastGameUpdate(player.userId, {
            balance: newBalance,
            cps: bonusedCPS,
            passiveIncome: passiveEarnings,
          });

          processedCount++;
        }
      } catch (error) {
        console.error(`Error processing online passive income for player ${player.id}:`, error);
      }
    }

    const duration = Date.now() - startTime;
    console.log(`✅ Online passive income processed: ${processedCount} players in ${duration}ms`);

    return {
      success: true,
      message: `Processed ${processedCount} online players`,
      duration,
      processedCount
    };
  } catch (error) {
    console.error('❌ Error in online passive income processing:', error);
    return { success: false, message: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Background job to update market prices
 * Simulates cryptocurrency market price changes
 */
export async function updateMarketPrices() {
  try {
    const startTime = Date.now();
    
    // Get the latest prices for each currency
    const latestPrices = await db.cryptocurrencyPrice.findMany({
      orderBy: {
        timestamp: 'desc'
      },
      distinct: ['currency'],
    });

    const now = new Date();
    const newPrices = [];

    // Update each currency price
    for (const currencyType of Object.keys(CURRENCY_CONFIGS)) {
      const currency = currencyType as CurrencyType;
      const latestPrice = latestPrices.find(p => p.currency === currency);
      const config = CURRENCY_CONFIGS[currency];
      
      // If no price exists yet, use the base value from config
      if (!latestPrice) {
        newPrices.push({
          currency,
          price: config.baseValue,
          volume24h: 1000, // Default volume
          volatility: config.volatility || 0.05,
          timestamp: now,
        });
        continue;
      }

      // Calculate new price based on volatility and time elapsed
      const timeElapsedMs = now.getTime() - latestPrice.timestamp.getTime();
      const volatility = Number(latestPrice.volatility) || config.volatility || 0.05;
      
      // Calculate new price with some randomness and mean reversion
      const oldPrice = Number(latestPrice.price);
      const newPrice = calculateMarketPrice(oldPrice, volatility, timeElapsedMs);
      
      // Calculate new volume with some randomness
      const oldVolume = Number(latestPrice.volume24h);
      const volumeChange = (Math.random() - 0.5) * 0.2; // -10% to +10%
      const newVolume = Math.max(100, oldVolume * (1 + volumeChange));
      
      // Calculate new volatility with some randomness and mean reversion
      const baseVolatility = config.volatility || 0.05;
      const volatilityChange = (Math.random() - 0.5) * 0.1; // -5% to +5%
      const meanReversion = (baseVolatility - volatility) * 0.1; // Pull back toward base volatility
      const newVolatility = Math.max(0.01, Math.min(0.5, volatility * (1 + volatilityChange) + meanReversion));
      
      newPrices.push({
        currency,
        price: newPrice,
        volume24h: newVolume,
        volatility: newVolatility,
        timestamp: now,
      });
    }

    // Insert all new prices in a single transaction
    if (newPrices.length > 0) {
      await db.cryptocurrencyPrice.createMany({
        data: newPrices,
      });
      
      const duration = Date.now() - startTime;
      console.log(`💹 Updated prices for ${newPrices.length} cryptocurrencies in ${duration}ms`);
      
      return { 
        success: true, 
        updatedCurrencies: newPrices.length,
        duration
      };
    }
    
    return { success: true, updatedCurrencies: 0 };
  } catch (error) {
    console.error('Error updating market prices:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Job execution statistics
 */
interface JobStats {
  name: string;
  lastRun: Date | null;
  successCount: number;
  errorCount: number;
  averageDuration: number;
  isRunning: boolean;
}

// Use global state to prevent multiple instances
declare global {
  var __backgroundJobScheduler: BackgroundJobScheduler | undefined;
}

/**
 * Enhanced background job scheduler with monitoring and error handling
 * In production, this would be replaced with a proper job queue like Bull or Agenda
 */
export class BackgroundJobScheduler {
  private intervals: NodeJS.Timeout[] = [];
  private jobStats: Map<string, JobStats> = new Map();
  private isShuttingDown = false;
  private isStarted = false;

  constructor() {
    // Initialize job statistics
    this.jobStats.set('offlineProduction', {
      name: 'Offline Production',
      lastRun: null,
      successCount: 0,
      errorCount: 0,
      averageDuration: 0,
      isRunning: false,
    });

    this.jobStats.set('onlinePassiveIncome', {
      name: 'Online Passive Income',
      lastRun: null,
      successCount: 0,
      errorCount: 0,
      averageDuration: 0,
      isRunning: false,
    });

    this.jobStats.set('rateLimitCleanup', {
      name: 'Rate Limit Cleanup',
      lastRun: null,
      successCount: 0,
      errorCount: 0,
      averageDuration: 0,
      isRunning: false,
    });

    this.jobStats.set('marketUpdate', {
      name: 'Market Update',
      lastRun: null,
      successCount: 0,
      errorCount: 0,
      averageDuration: 0,
      isRunning: false,
    });
  }

  private async executeJob(jobName: string, jobFunction: () => Promise<any>) {
    const stats = this.jobStats.get(jobName);
    if (!stats || stats.isRunning || this.isShuttingDown) {
      return;
    }

    stats.isRunning = true;
    const startTime = Date.now();

    try {
      const result = await jobFunction();
      const duration = Date.now() - startTime;
      
      // Update statistics
      stats.lastRun = new Date();
      stats.successCount++;
      stats.averageDuration = (stats.averageDuration * (stats.successCount - 1) + duration) / stats.successCount;
      stats.isRunning = false;

      console.log(`Job ${jobName} completed successfully in ${duration}ms`, result);
    } catch (error) {
      const duration = Date.now() - startTime;
      
      stats.lastRun = new Date();
      stats.errorCount++;
      stats.isRunning = false;

      console.error(`Job ${jobName} failed after ${duration}ms:`, error);
    }
  }

  start() {
    if (this.isStarted || this.intervals.length > 0) {
      console.log('Background job scheduler is already running');
      return;
    }

    console.log('Starting background job scheduler...');
    this.isShuttingDown = false;
    this.isStarted = true;

    // Process offline production every 5 minutes
    const offlineProductionInterval = setInterval(() => {
      this.executeJob('offlineProduction', processOfflineProduction);
    }, 5 * 60 * 1000);

    // Process online passive income every 60 seconds
    // Note: The job calculates income for a fixed 60-second period to ensure consistent earnings
    const onlinePassiveIncomeInterval = setInterval(() => {
      this.executeJob('onlinePassiveIncome', processOnlinePassiveIncome);
    }, 60 * 1000);

    // Clean up rate limit data every hour
    const rateLimitCleanupInterval = setInterval(() => {
      this.executeJob('rateLimitCleanup', cleanupRateLimitData);
    }, 60 * 60 * 1000);

    // Update market prices every 30 seconds (placeholder)
    const marketUpdateInterval = setInterval(() => {
      this.executeJob('marketUpdate', updateMarketPrices);
    }, 30 * 1000);

    // Job monitoring every 10 minutes
    const monitoringInterval = setInterval(() => {
      this.logJobStatistics();
    }, 10 * 60 * 1000);

    this.intervals.push(
      offlineProductionInterval,
      onlinePassiveIncomeInterval,
      rateLimitCleanupInterval,
      marketUpdateInterval,
      monitoringInterval
    );

    // Run initial jobs with delays to avoid startup conflicts
    setTimeout(() => this.executeJob('offlineProduction', processOfflineProduction), 1000);
    setTimeout(() => this.executeJob('rateLimitCleanup', cleanupRateLimitData), 2000);
    // Wait longer for WebSocket to be ready before starting online passive income
    setTimeout(() => this.executeJob('onlinePassiveIncome', processOnlinePassiveIncome), 10000);
  }

  stop() {
    console.log('Stopping background job scheduler...');
    this.isShuttingDown = true;
    
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
    
    // Wait for running jobs to complete
    const runningJobs = Array.from(this.jobStats.values()).filter(stats => stats.isRunning);
    if (runningJobs.length > 0) {
      console.log(`Waiting for ${runningJobs.length} running jobs to complete...`);
      // In production, you might want to implement a timeout here
    }
  }

  getJobStatistics(): JobStats[] {
    return Array.from(this.jobStats.values());
  }

  private logJobStatistics() {
    console.log('\n=== Background Job Statistics ===');
    for (const stats of this.jobStats.values()) {
      console.log(`${stats.name}:`);
      console.log(`  Last Run: ${stats.lastRun?.toISOString() || 'Never'}`);
      console.log(`  Success: ${stats.successCount}, Errors: ${stats.errorCount}`);
      console.log(`  Average Duration: ${stats.averageDuration.toFixed(2)}ms`);
      console.log(`  Currently Running: ${stats.isRunning}`);
    }
    console.log('================================\n');
  }
}

// Export singleton instance with global state management
export const backgroundJobScheduler = global.__backgroundJobScheduler ?? (global.__backgroundJobScheduler = new BackgroundJobScheduler());