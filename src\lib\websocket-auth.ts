import jwt from 'jsonwebtoken';
import type { Session } from 'next-auth';

const JWT_SECRET = process.env.AUTH_SECRET || 'fallback-secret-for-development';

export interface WebSocketTokenPayload {
  userId: string;
  sessionId?: string;
  iat: number;
  exp: number;
}

/**
 * Generate a JWT token for WebSocket authentication
 */
export function generateWebSocketToken(userId: string, sessionId?: string): string {
  const payload: Omit<WebSocketTokenPayload, 'iat' | 'exp'> = {
    userId,
    sessionId,
  };

  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '24h', // Token expires in 24 hours
    issuer: 'cryptotycoon-websocket',
  });
}

/**
 * Verify and decode a WebSocket JWT token
 */
export function verifyWebSocketToken(token: string): WebSocketTokenPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'cryptotycoon-websocket',
    }) as WebSocketTokenPayload;
    
    return decoded;
  } catch (error) {
    console.error('WebSocket token verification failed:', error);
    return null;
  }
}

/**
 * Generate a WebSocket token from a NextAuth session
 */
export async function generateTokenFromSession(session: Session | null): Promise<string | null> {
  if (!session?.user?.id) {
    return null;
  }

  return generateWebSocketToken(session.user.id);
}

/**
 * Create a WebSocket token endpoint for client-side requests
 */
export async function createWebSocketTokenFromRequest(headers: Headers): Promise<string | null> {
  try {
    // Import auth dynamically to avoid module loading issues
    const { auth } = await import('~/server/auth');
    const session = await auth();

    if (!session?.user?.id) {
      return null;
    }

    return generateWebSocketToken(session.user.id);
  } catch (error) {
    console.error('Failed to create WebSocket token from request:', error);
    return null;
  }
}
