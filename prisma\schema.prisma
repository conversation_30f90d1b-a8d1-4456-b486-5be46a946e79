generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Player {
  id               String                   @id @default(cuid())
  userId           String                   @unique
  cryptoCoin       Decimal                  @default(0) @db.Decimal(20, 8)
  ethereumG        Decimal                  @default(0) @db.Decimal(20, 8)
  byteCoin         Decimal                  @default(0) @db.Decimal(20, 8)
  solanaX          Decimal                  @default(0) @db.Decimal(20, 8)
  totalClicks      BigInt                   @default(0)
  prestigeLevel    Int                      @default(0)
  blockchainPoints Int                      @default(0)
  lastActiveAt     DateTime                 @default(now())
  createdAt        DateTime                 @default(now())
  updatedAt        DateTime                 @updatedAt
  cardanoZ         Decimal                  @default(0) @db.Decimal(20, 8)
  polkadotY        Decimal                  @default(0) @db.Decimal(20, 8)
  daoProposals     DAOProposal[]
  daoVotes         DAOVote[]
  miningRigs       MiningRig[]
  user             User                     @relation(fields: [userId], references: [id], onDelete: Cascade)
  currencySettings PlayerCurrencySettings[]
  dataCenters      PlayerDataCenter[]
  research         PlayerResearch[]
  shards           PlayerShard[]
  syndicateLeader  Syndicate?
  syndicateMember  SyndicateMember?
  buyerTrades      Trade[]                  @relation("BuyerTrades")
  sellerTrades     Trade[]                  @relation("SellerTrades")
  tradeListings    TradeListings[]

  @@index([userId])
  @@index([lastActiveAt])
}

model MiningRig {
  id         String   @id @default(cuid())
  playerId   String
  rigType    RigType
  level      Int      @default(1)
  quantity   Int      @default(1)
  baseOutput Decimal  @db.Decimal(20, 8)
  efficiency Decimal  @default(1.0) @db.Decimal(10, 4)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  player     Player   @relation(fields: [playerId], references: [id], onDelete: Cascade)

  @@index([playerId])
  @@index([rigType])
}

model BlockchainShard {
  id            String          @id @default(cuid())
  name          String          @unique
  rarity        ShardRarity
  category      ShardCategory
  effects       Json
  description   String
  playerShards  PlayerShard[]
  tradeListings TradeListings[]

  @@index([rarity])
  @@index([category])
}

model PlayerShard {
  id         String          @id @default(cuid())
  playerId   String
  shardId    String
  quantity   Int             @default(1)
  equipped   Boolean         @default(false)
  acquiredAt DateTime        @default(now())
  player     Player          @relation(fields: [playerId], references: [id], onDelete: Cascade)
  shard      BlockchainShard @relation(fields: [shardId], references: [id])

  @@index([playerId])
  @@index([shardId])
  @@index([equipped])
}

model PlayerResearch {
  id          String    @id @default(cuid())
  playerId    String
  researchId  String
  level       Int       @default(1)
  isCompleted Boolean   @default(false)
  startedAt   DateTime  @default(now())
  completedAt DateTime?
  player      Player    @relation(fields: [playerId], references: [id], onDelete: Cascade)

  @@unique([playerId, researchId])
  @@index([playerId])
}

model PlayerDataCenter {
  id            String   @id @default(cuid())
  playerId      String
  locationId    String
  level         Int      @default(1)
  isActive      Boolean  @default(true)
  establishedAt DateTime @default(now())
  player        Player   @relation(fields: [playerId], references: [id], onDelete: Cascade)

  @@unique([playerId, locationId])
  @@index([playerId])
}

model CryptocurrencyPrice {
  id         String       @id @default(cuid())
  currency   CurrencyType
  price      Decimal      @db.Decimal(20, 8)
  volume24h  Decimal      @db.Decimal(20, 8)
  volatility Decimal      @db.Decimal(10, 4)
  timestamp  DateTime     @default(now())

  @@index([currency])
  @@index([timestamp])
}

model CurrencySettings {
  id                String       @id @default(cuid())
  currency          CurrencyType @unique
  name              String
  symbol            String
  color             String
  description       String
  baseValue         Decimal      @db.Decimal(20, 8)
  isEnabled         Boolean      @default(true)
  isUnlocked        Boolean      @default(false)
  unlockRequirement Json?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt

  @@index([isEnabled])
  @@index([isUnlocked])
}

model PlayerCurrencySettings {
  id             String       @id @default(cuid())
  playerId       String
  currency       CurrencyType
  isUnlocked     Boolean      @default(false)
  isMiningTarget Boolean      @default(false)
  lastMined      DateTime?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  player         Player       @relation(fields: [playerId], references: [id], onDelete: Cascade)

  @@unique([playerId, currency])
  @@index([playerId])
  @@index([currency])
  @@index([isMiningTarget])
}

model TradeListings {
  id        String          @id @default(cuid())
  sellerId  String
  shardId   String
  price     Decimal         @db.Decimal(20, 8)
  currency  CurrencyType
  quantity  Int
  isActive  Boolean         @default(true)
  createdAt DateTime        @default(now())
  expiresAt DateTime?
  seller    Player          @relation(fields: [sellerId], references: [id], onDelete: Cascade)
  shard     BlockchainShard @relation(fields: [shardId], references: [id])

  @@index([sellerId])
  @@index([shardId])
  @@index([isActive])
  @@index([createdAt])
}

model Trade {
  id          String       @id @default(cuid())
  buyerId     String
  sellerId    String
  shardId     String
  price       Decimal      @db.Decimal(20, 8)
  currency    CurrencyType
  quantity    Int
  completedAt DateTime     @default(now())
  buyer       Player       @relation("BuyerTrades", fields: [buyerId], references: [id])
  seller      Player       @relation("SellerTrades", fields: [sellerId], references: [id])

  @@index([buyerId])
  @@index([sellerId])
  @@index([completedAt])
}

model Syndicate {
  id          String            @id @default(cuid())
  name        String            @unique
  description String?
  leaderId    String            @unique
  level       Int               @default(1)
  experience  BigInt            @default(0)
  createdAt   DateTime          @default(now())
  leader      Player            @relation(fields: [leaderId], references: [id])
  members     SyndicateMember[]

  @@index([name])
  @@index([level])
}

model SyndicateMember {
  id           String        @id @default(cuid())
  playerId     String        @unique
  syndicateId  String
  role         SyndicateRole @default(MEMBER)
  joinedAt     DateTime      @default(now())
  contribution BigInt        @default(0)
  player       Player        @relation(fields: [playerId], references: [id], onDelete: Cascade)
  syndicate    Syndicate     @relation(fields: [syndicateId], references: [id], onDelete: Cascade)

  @@index([syndicateId])
  @@index([role])
}

model DAOProposal {
  id           String         @id @default(cuid())
  title        String
  description  String
  proposerId   String
  votesFor     Int            @default(0)
  votesAgainst Int            @default(0)
  status       ProposalStatus @default(ACTIVE)
  createdAt    DateTime       @default(now())
  expiresAt    DateTime
  proposer     Player         @relation(fields: [proposerId], references: [id])
  votes        DAOVote[]

  @@index([proposerId])
  @@index([status])
  @@index([expiresAt])
}

model DAOVote {
  id         String      @id @default(cuid())
  proposalId String
  voterId    String
  vote       VoteChoice
  weight     Int
  castAt     DateTime    @default(now())
  proposal   DAOProposal @relation(fields: [proposalId], references: [id], onDelete: Cascade)
  voter      Player      @relation(fields: [voterId], references: [id])

  @@unique([proposalId, voterId])
  @@index([proposalId])
  @@index([voterId])
}

model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String?
  access_token             String?
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String?
  session_state            String?
  refresh_token_expires_in Int?
  user                     User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  player        Player?
  sessions      Session[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum RigType {
  USB_ASIC
  GPU_FARM
  ASIC_MINER
  QUANTUM_PROCESSOR
  FUSION_REACTOR
  DYSON_SPHERE
}

enum ShardRarity {
  COMMON
  UNCOMMON
  RARE
  EPIC
  LEGENDARY
  MYTHIC
}

enum ShardCategory {
  MINING_BOOST
  EFFICIENCY
  MARKET_ADVANTAGE
  SPECIAL_ABILITY
  PRESTIGE_BONUS
}

enum CurrencyType {
  CRYPTO_COIN
  ETHEREUM_G
  BYTE_COIN
  SOLANA_X
  CARDANO_Z
  POLKADOT_Y
  AVALANCHE_A
  COSMOS_C
  TEZOS_T
  ALGORAND_ALGO
}

enum SyndicateRole {
  LEADER
  OFFICER
  MEMBER
}

enum ProposalStatus {
  ACTIVE
  PASSED
  REJECTED
  EXPIRED
}

enum VoteChoice {
  FOR
  AGAINST
  ABSTAIN
}
