import type { NextApiRequest, NextApiResponse } from 'next';
import { db } from '~/server/db';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    console.log('🔍 Debug session request');
    console.log('🔍 Cookies:', req.headers.cookie);
    
    const cookies = req.headers.cookie;
    if (!cookies) {
      return res.status(200).json({ error: 'No cookies' });
    }

    // Try different cookie patterns
    const patterns = [
      /next-auth\.session-token=([^;]+)/,
      /authjs\.session-token=([^;]+)/,
      /__Secure-next-auth\.session-token=([^;]+)/,
      /__Secure-authjs\.session-token=([^;]+)/,
    ];

    let sessionToken = null;
    for (const pattern of patterns) {
      const match = cookies.match(pattern);
      if (match) {
        sessionToken = match[1];
        console.log('🔍 Found session token with pattern:', pattern.source);
        break;
      }
    }

    if (!sessionToken) {
      return res.status(200).json({ 
        error: 'No session token found',
        cookies: cookies.split(';').map(c => c.trim())
      });
    }

    // Look up the session
    const session = await db.session.findUnique({
      where: { sessionToken },
      include: { user: true }
    });

    if (!session) {
      return res.status(200).json({ 
        error: 'Session not found in database',
        sessionToken: sessionToken.substring(0, 10) + '...'
      });
    }

    return res.status(200).json({
      success: true,
      userId: session.user.id,
      userEmail: session.user.email,
      expires: session.expires,
      isExpired: session.expires < new Date()
    });

  } catch (error) {
    console.error('Debug session error:', error);
    return res.status(500).json({ error: error.message });
  }
}
