{"name": "cryptotycoon", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "dev": "next dev --turbo", "postinstall": "prisma generate", "preview": "next build && next start", "start": "next start", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.5.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@types/jsonwebtoken": "^9.0.10", "jsonwebtoken": "^9.0.2", "next": "^15.2.3", "next-auth": "5.0.0-beta.25", "react": "^19.0.0", "react-dom": "^19.0.0", "server-only": "^0.0.1", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "superjson": "^2.2.1", "zod": "^3.24.2", "zustand": "^4.4.7"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitest/ui": "^3.2.4", "postcss": "^8.5.3", "prisma": "^6.5.0", "tailwindcss": "^4.0.15", "tsx": "^4.20.3", "typescript": "^5.8.2", "vitest": "^3.2.4"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "npm@10.9.2"}