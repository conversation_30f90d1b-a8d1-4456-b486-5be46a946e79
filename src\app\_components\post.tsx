"use client";

import { api } from "~/trpc/react";

export function GameStatus() {
	const secretMessage = api.post.getSecretMessage.useQuery();

	return (
		<div className="w-full max-w-xs">
			<div className="rounded-lg bg-white/10 p-4">
				<h3 className="text-lg font-semibold text-white mb-2">Game Status</h3>
				<p className="text-white/80">
					{secretMessage.data ? secretMessage.data : "Loading game status..."}
				</p>
				<div className="mt-4 space-y-2">
					<div className="flex justify-between text-sm">
						<span className="text-white/60">Database:</span>
						<span className="text-green-400">Connected</span>
					</div>
					<div className="flex justify-between text-sm">
						<span className="text-white/60">Game Models:</span>
						<span className="text-green-400">Ready</span>
					</div>
					<div className="flex justify-between text-sm">
						<span className="text-white/60">Blockchain Shards:</span>
						<span className="text-green-400">14 Loaded</span>
					</div>
				</div>
			</div>
		</div>
	);
}
