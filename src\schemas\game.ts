import { z } from "zod";
import { 
  RigType, 
  ShardRarity, 
  ShardCategory, 
  CurrencyType, 
  SyndicateRole, 
  ProposalStatus, 
  VoteChoice 
} from "../types/game";

// Base schemas for common types
export const DecimalSchema = z.union([
  z.number(),
  z.string().regex(/^\d+(\.\d+)?$/),
  z.object({
    toNumber: z.function().returns(z.number()),
  }),
]);

export const BigIntSchema = z.union([
  z.bigint(),
  z.number().int(),
  z.string().regex(/^\d+$/),
]);

// Enum schemas
export const RigTypeSchema = z.nativeEnum(RigType);
export const ShardRaritySchema = z.nativeEnum(ShardRarity);
export const ShardCategorySchema = z.nativeEnum(ShardCategory);
export const CurrencyTypeSchema = z.nativeEnum(CurrencyType);
export const SyndicateRoleSchema = z.nativeEnum(SyndicateRole);
export const ProposalStatusSchema = z.nativeEnum(ProposalStatus);
export const VoteChoiceSchema = z.nativeEnum(VoteChoice);

// Core game schemas
export const PlayerStateSchema = z.object({
  id: z.string().cuid(),
  userId: z.string().cuid(),
  currencies: z.object({
    cryptoCoin: DecimalSchema,
    ethereumG: DecimalSchema,
    byteCoin: DecimalSchema,
    solanaX: DecimalSchema,
    cardanoZ: DecimalSchema,
    polkadotY: DecimalSchema,
    avalancheA: DecimalSchema.optional(),
    cosmosC: DecimalSchema.optional(),
    tezosT: DecimalSchema.optional(),
    algorandAlgo: DecimalSchema.optional(),
  }),
  stats: z.object({
    totalClicks: BigIntSchema,
    prestigeLevel: z.number().int().min(0),
    blockchainPoints: z.number().int().min(0),
  }),
  timestamps: z.object({
    lastActiveAt: z.date(),
    createdAt: z.date(),
    updatedAt: z.date(),
  }),
  activeMiningCurrency: CurrencyTypeSchema.optional(),
});

export const ShardEffectSchema = z.object({
  type: z.enum(['MULTIPLIER', 'FLAT_BONUS', 'SPECIAL_ABILITY']),
  target: z.string().min(1),
  value: z.number(),
  conditions: z.array(z.string()).optional(),
});

export const BlockchainShardSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1).max(100),
  rarity: ShardRaritySchema,
  category: ShardCategorySchema,
  effects: z.array(ShardEffectSchema),
  description: z.string().min(1).max(1000),
});

export const MiningRigSchema = z.object({
  id: z.string().cuid(),
  playerId: z.string().cuid(),
  rigType: RigTypeSchema,
  level: z.number().int().min(1).max(1000),
  quantity: z.number().int().min(1),
  baseOutput: DecimalSchema,
  efficiency: DecimalSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const PlayerShardSchema = z.object({
  id: z.string().cuid(),
  playerId: z.string().cuid(),
  shardId: z.string().cuid(),
  quantity: z.number().int().min(1),
  equipped: z.boolean(),
  acquiredAt: z.date(),
});

export const PlayerResearchSchema = z.object({
  id: z.string().cuid(),
  playerId: z.string().cuid(),
  researchId: z.string().min(1),
  level: z.number().int().min(1),
  isCompleted: z.boolean(),
  startedAt: z.date(),
  completedAt: z.date().optional(),
});

export const PlayerDataCenterSchema = z.object({
  id: z.string().cuid(),
  playerId: z.string().cuid(),
  locationId: z.string().min(1),
  level: z.number().int().min(1),
  isActive: z.boolean(),
  establishedAt: z.date(),
});

// Market schemas
export const CryptocurrencyPriceSchema = z.object({
  id: z.string().cuid(),
  currency: CurrencyTypeSchema,
  price: DecimalSchema,
  volume24h: DecimalSchema,
  volatility: DecimalSchema,
  timestamp: z.date(),
});

export const TradeListingsSchema = z.object({
  id: z.string().cuid(),
  sellerId: z.string().cuid(),
  shardId: z.string().cuid(),
  price: DecimalSchema,
  currency: CurrencyTypeSchema,
  quantity: z.number().int().min(1),
  isActive: z.boolean(),
  createdAt: z.date(),
  expiresAt: z.date().optional(),
});

export const TradeSchema = z.object({
  id: z.string().cuid(),
  buyerId: z.string().cuid(),
  sellerId: z.string().cuid(),
  shardId: z.string().cuid(),
  price: DecimalSchema,
  currency: CurrencyTypeSchema,
  quantity: z.number().int().min(1),
  completedAt: z.date(),
});

// Social features schemas
export const SyndicateSchema = z.object({
  id: z.string().cuid(),
  name: z.string().min(1).max(50),
  description: z.string().max(500).optional(),
  leaderId: z.string().cuid(),
  level: z.number().int().min(1),
  experience: BigIntSchema,
  createdAt: z.date(),
});

export const SyndicateMemberSchema = z.object({
  id: z.string().cuid(),
  playerId: z.string().cuid(),
  syndicateId: z.string().cuid(),
  role: SyndicateRoleSchema,
  joinedAt: z.date(),
  contribution: BigIntSchema,
});

export const DAOProposalSchema = z.object({
  id: z.string().cuid(),
  title: z.string().min(1).max(200),
  description: z.string().min(1).max(2000),
  proposerId: z.string().cuid(),
  votesFor: z.number().int().min(0),
  votesAgainst: z.number().int().min(0),
  status: ProposalStatusSchema,
  createdAt: z.date(),
  expiresAt: z.date(),
});

export const DAOVoteSchema = z.object({
  id: z.string().cuid(),
  proposalId: z.string().cuid(),
  voterId: z.string().cuid(),
  vote: VoteChoiceSchema,
  weight: z.number().int().min(1),
  castAt: z.date(),
});

// Input validation schemas for API endpoints
export const ClickMineInputSchema = z.object({
  timestamp: z.number().optional(),
  clickCount: z.number().int().min(1).max(50).optional().default(1), // Allow batch clicks up to 50
});

export const PurchaseRigInputSchema = z.object({
  rigType: RigTypeSchema,
  quantity: z.number().int().min(1).max(100),
});

export const UpgradeRigInputSchema = z.object({
  rigId: z.string().cuid(),
  levels: z.number().int().min(1).max(10),
});

export const SwitchMiningTargetInputSchema = z.object({
  currency: CurrencyTypeSchema,
});

export const CurrencyUnlockRequirementSchema = z.object({
  type: z.enum(['RESEARCH', 'CURRENCY', 'PRESTIGE_LEVEL', 'CLICKS']),
  value: z.number(),
  currencyType: CurrencyTypeSchema.optional(),
});

export const CurrencySettingsSchema = z.object({
  id: z.string().cuid(),
  currency: CurrencyTypeSchema,
  name: z.string().min(1).max(50),
  symbol: z.string().min(1).max(10),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/),
  description: z.string().min(1).max(500),
  baseValue: DecimalSchema,
  isEnabled: z.boolean(),
  isUnlocked: z.boolean(),
  unlockRequirement: CurrencyUnlockRequirementSchema.optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const PlayerCurrencySettingsSchema = z.object({
  id: z.string().cuid(),
  playerId: z.string().cuid(),
  currency: CurrencyTypeSchema,
  isUnlocked: z.boolean(),
  isMiningTarget: z.boolean(),
  lastMined: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const CreateTradeListingInputSchema = z.object({
  shardId: z.string().cuid(),
  price: z.number().positive(),
  currency: CurrencyTypeSchema,
  quantity: z.number().int().min(1),
  expiresIn: z.number().int().min(3600).max(604800).optional(), // 1 hour to 1 week
});

export const ExecuteTradeInputSchema = z.object({
  listingId: z.string().cuid(),
  quantity: z.number().int().min(1),
});

export const CreateSyndicateInputSchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string().max(500).optional(),
});

export const JoinSyndicateInputSchema = z.object({
  syndicateId: z.string().cuid(),
});

export const CreateDAOProposalInputSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().min(1).max(2000),
  votingDuration: z.number().int().min(86400).max(604800), // 1 day to 1 week
});

export const CastDAOVoteInputSchema = z.object({
  proposalId: z.string().cuid(),
  vote: VoteChoiceSchema,
});

export const InvestInResearchInputSchema = z.object({
  researchId: z.string().min(1),
  amount: z.number().positive(),
  currency: CurrencyTypeSchema,
});

export const EstablishDataCenterInputSchema = z.object({
  locationId: z.string().min(1),
});

// Configuration schemas
export const RigConfigSchema = z.object({
  type: RigTypeSchema,
  name: z.string().min(1),
  baseCost: z.number().positive(),
  baseOutput: z.number().positive(),
  costMultiplier: z.number().min(1),
  unlockRequirement: z.object({
    type: z.enum(['CLICKS', 'CURRENCY', 'RESEARCH']),
    value: z.number().positive(),
  }).optional(),
});

export const ResearchConfigSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  description: z.string().min(1),
  cost: z.number().positive(),
  currency: CurrencyTypeSchema,
  prerequisites: z.array(z.string()),
  unlocks: z.array(z.string()),
});

export const LocationConfigSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  description: z.string().min(1),
  unlockCost: z.number().positive(),
  currency: CurrencyTypeSchema,
  bonuses: z.array(z.object({
    type: z.string(),
    value: z.number(),
  })),
  requirements: z.array(z.object({
    type: z.string(),
    value: z.number(),
  })).optional(),
});

// Response schemas
export const GameStateResponseSchema = z.object({
  player: PlayerStateSchema,
  miningRigs: z.array(MiningRigSchema),
  shards: z.array(PlayerShardSchema),
  research: z.array(PlayerResearchSchema),
  dataCenters: z.array(PlayerDataCenterSchema),
  marketData: z.object({
    currencies: z.record(CurrencyTypeSchema, z.object({
      price: z.number(),
      change24h: z.number(),
      volume: z.number(),
      volatility: z.number(),
    })),
  }),
});

export const ClickMineResponseSchema = z.object({
  success: z.boolean(),
  earnedAmount: z.number(),
  newBalance: z.number(),
  clickCount: z.number(),
});

export const PurchaseRigResponseSchema = z.object({
  success: z.boolean(),
  rigId: z.string().cuid(),
  totalCost: z.number(),
  newBalance: z.number(),
});

export const OfflineProductionResponseSchema = z.object({
  success: z.boolean(),
  offlineTime: z.number(), // in seconds
  totalProduction: z.number(),
  newBalance: z.number(),
});

// Error schemas
export const GameErrorSchema = z.object({
  code: z.string(),
  message: z.string(),
  details: z.record(z.any()).optional(),
});

// Utility schemas for validation
export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
});

export const SortOrderSchema = z.enum(['asc', 'desc']).default('desc');

export const DateRangeSchema = z.object({
  from: z.date(),
  to: z.date(),
}).refine((data) => data.from <= data.to, {
  message: "From date must be before or equal to to date",
});