-- CreateEnum
CREATE TYPE "RigType" AS ENUM ('USB_ASIC', 'GPU_FARM', 'ASIC_MINER', 'QUANTUM_PROCESSOR', 'FUSION_REACTOR', 'DYSON_SPHERE');

-- C<PERSON><PERSON>num
CREATE TYPE "ShardRarity" AS ENUM ('COMMON', 'UNCOMMON', 'RARE', 'EPIC', 'LEGENDARY', 'MYTHIC');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ShardCategory" AS ENUM ('MINING_BOOST', 'EFFICIENCY', 'MARKET_ADVANTAGE', 'SPECIAL_ABILITY', 'PRESTIGE_BONUS');

-- C<PERSON><PERSON>num
CREATE TYPE "CurrencyType" AS ENUM ('CRYPTO_COIN', 'ETHEREUM_G', 'BYTE_COIN', 'SOLANA_X', 'CARDANO_Z', 'POLKADOT_Y');

-- CreateEnum
CREATE TYPE "SyndicateRole" AS ENUM ('LEADER', 'OFFICER', 'MEMBER');

-- CreateEnum
CREATE TYPE "ProposalStatus" AS ENUM ('ACTIVE', 'PASSED', 'REJECTED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "VoteChoice" AS ENUM ('FOR', 'AGAINST', 'ABSTAIN');

-- CreateTable
CREATE TABLE "Player" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "cryptoCoin" DECIMAL(20,8) NOT NULL DEFAULT 0,
    "ethereumG" DECIMAL(20,8) NOT NULL DEFAULT 0,
    "byteCoin" DECIMAL(20,8) NOT NULL DEFAULT 0,
    "solanaX" DECIMAL(20,8) NOT NULL DEFAULT 0,
    "totalClicks" BIGINT NOT NULL DEFAULT 0,
    "prestigeLevel" INTEGER NOT NULL DEFAULT 0,
    "blockchainPoints" INTEGER NOT NULL DEFAULT 0,
    "lastActiveAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Player_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MiningRig" (
    "id" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "rigType" "RigType" NOT NULL,
    "level" INTEGER NOT NULL DEFAULT 1,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "baseOutput" DECIMAL(20,8) NOT NULL,
    "efficiency" DECIMAL(10,4) NOT NULL DEFAULT 1.0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MiningRig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BlockchainShard" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "rarity" "ShardRarity" NOT NULL,
    "category" "ShardCategory" NOT NULL,
    "effects" JSONB NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "BlockchainShard_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlayerShard" (
    "id" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "shardId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "equipped" BOOLEAN NOT NULL DEFAULT false,
    "acquiredAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PlayerShard_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlayerResearch" (
    "id" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "researchId" TEXT NOT NULL,
    "level" INTEGER NOT NULL DEFAULT 1,
    "isCompleted" BOOLEAN NOT NULL DEFAULT false,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "PlayerResearch_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlayerDataCenter" (
    "id" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "level" INTEGER NOT NULL DEFAULT 1,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "establishedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PlayerDataCenter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CryptocurrencyPrice" (
    "id" TEXT NOT NULL,
    "currency" "CurrencyType" NOT NULL,
    "price" DECIMAL(20,8) NOT NULL,
    "volume24h" DECIMAL(20,8) NOT NULL,
    "volatility" DECIMAL(10,4) NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CryptocurrencyPrice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TradeListings" (
    "id" TEXT NOT NULL,
    "sellerId" TEXT NOT NULL,
    "shardId" TEXT NOT NULL,
    "price" DECIMAL(20,8) NOT NULL,
    "currency" "CurrencyType" NOT NULL,
    "quantity" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),

    CONSTRAINT "TradeListings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Trade" (
    "id" TEXT NOT NULL,
    "buyerId" TEXT NOT NULL,
    "sellerId" TEXT NOT NULL,
    "shardId" TEXT NOT NULL,
    "price" DECIMAL(20,8) NOT NULL,
    "currency" "CurrencyType" NOT NULL,
    "quantity" INTEGER NOT NULL,
    "completedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Trade_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Syndicate" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "leaderId" TEXT NOT NULL,
    "level" INTEGER NOT NULL DEFAULT 1,
    "experience" BIGINT NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Syndicate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SyndicateMember" (
    "id" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "syndicateId" TEXT NOT NULL,
    "role" "SyndicateRole" NOT NULL DEFAULT 'MEMBER',
    "joinedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "contribution" BIGINT NOT NULL DEFAULT 0,

    CONSTRAINT "SyndicateMember_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DAOProposal" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "proposerId" TEXT NOT NULL,
    "votesFor" INTEGER NOT NULL DEFAULT 0,
    "votesAgainst" INTEGER NOT NULL DEFAULT 0,
    "status" "ProposalStatus" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DAOProposal_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DAOVote" (
    "id" TEXT NOT NULL,
    "proposalId" TEXT NOT NULL,
    "voterId" TEXT NOT NULL,
    "vote" "VoteChoice" NOT NULL,
    "weight" INTEGER NOT NULL,
    "castAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DAOVote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Account" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    "refresh_token_expires_in" INTEGER,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "Player_userId_key" ON "Player"("userId");

-- CreateIndex
CREATE INDEX "Player_userId_idx" ON "Player"("userId");

-- CreateIndex
CREATE INDEX "Player_lastActiveAt_idx" ON "Player"("lastActiveAt");

-- CreateIndex
CREATE INDEX "MiningRig_playerId_idx" ON "MiningRig"("playerId");

-- CreateIndex
CREATE INDEX "MiningRig_rigType_idx" ON "MiningRig"("rigType");

-- CreateIndex
CREATE UNIQUE INDEX "BlockchainShard_name_key" ON "BlockchainShard"("name");

-- CreateIndex
CREATE INDEX "BlockchainShard_rarity_idx" ON "BlockchainShard"("rarity");

-- CreateIndex
CREATE INDEX "BlockchainShard_category_idx" ON "BlockchainShard"("category");

-- CreateIndex
CREATE INDEX "PlayerShard_playerId_idx" ON "PlayerShard"("playerId");

-- CreateIndex
CREATE INDEX "PlayerShard_shardId_idx" ON "PlayerShard"("shardId");

-- CreateIndex
CREATE INDEX "PlayerShard_equipped_idx" ON "PlayerShard"("equipped");

-- CreateIndex
CREATE INDEX "PlayerResearch_playerId_idx" ON "PlayerResearch"("playerId");

-- CreateIndex
CREATE UNIQUE INDEX "PlayerResearch_playerId_researchId_key" ON "PlayerResearch"("playerId", "researchId");

-- CreateIndex
CREATE INDEX "PlayerDataCenter_playerId_idx" ON "PlayerDataCenter"("playerId");

-- CreateIndex
CREATE UNIQUE INDEX "PlayerDataCenter_playerId_locationId_key" ON "PlayerDataCenter"("playerId", "locationId");

-- CreateIndex
CREATE INDEX "CryptocurrencyPrice_currency_idx" ON "CryptocurrencyPrice"("currency");

-- CreateIndex
CREATE INDEX "CryptocurrencyPrice_timestamp_idx" ON "CryptocurrencyPrice"("timestamp");

-- CreateIndex
CREATE INDEX "TradeListings_sellerId_idx" ON "TradeListings"("sellerId");

-- CreateIndex
CREATE INDEX "TradeListings_shardId_idx" ON "TradeListings"("shardId");

-- CreateIndex
CREATE INDEX "TradeListings_isActive_idx" ON "TradeListings"("isActive");

-- CreateIndex
CREATE INDEX "TradeListings_createdAt_idx" ON "TradeListings"("createdAt");

-- CreateIndex
CREATE INDEX "Trade_buyerId_idx" ON "Trade"("buyerId");

-- CreateIndex
CREATE INDEX "Trade_sellerId_idx" ON "Trade"("sellerId");

-- CreateIndex
CREATE INDEX "Trade_completedAt_idx" ON "Trade"("completedAt");

-- CreateIndex
CREATE UNIQUE INDEX "Syndicate_name_key" ON "Syndicate"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Syndicate_leaderId_key" ON "Syndicate"("leaderId");

-- CreateIndex
CREATE INDEX "Syndicate_name_idx" ON "Syndicate"("name");

-- CreateIndex
CREATE INDEX "Syndicate_level_idx" ON "Syndicate"("level");

-- CreateIndex
CREATE UNIQUE INDEX "SyndicateMember_playerId_key" ON "SyndicateMember"("playerId");

-- CreateIndex
CREATE INDEX "SyndicateMember_syndicateId_idx" ON "SyndicateMember"("syndicateId");

-- CreateIndex
CREATE INDEX "SyndicateMember_role_idx" ON "SyndicateMember"("role");

-- CreateIndex
CREATE INDEX "DAOProposal_proposerId_idx" ON "DAOProposal"("proposerId");

-- CreateIndex
CREATE INDEX "DAOProposal_status_idx" ON "DAOProposal"("status");

-- CreateIndex
CREATE INDEX "DAOProposal_expiresAt_idx" ON "DAOProposal"("expiresAt");

-- CreateIndex
CREATE INDEX "DAOVote_proposalId_idx" ON "DAOVote"("proposalId");

-- CreateIndex
CREATE INDEX "DAOVote_voterId_idx" ON "DAOVote"("voterId");

-- CreateIndex
CREATE UNIQUE INDEX "DAOVote_proposalId_voterId_key" ON "DAOVote"("proposalId", "voterId");

-- CreateIndex
CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token");

-- AddForeignKey
ALTER TABLE "Player" ADD CONSTRAINT "Player_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MiningRig" ADD CONSTRAINT "MiningRig_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerShard" ADD CONSTRAINT "PlayerShard_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerShard" ADD CONSTRAINT "PlayerShard_shardId_fkey" FOREIGN KEY ("shardId") REFERENCES "BlockchainShard"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerResearch" ADD CONSTRAINT "PlayerResearch_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PlayerDataCenter" ADD CONSTRAINT "PlayerDataCenter_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TradeListings" ADD CONSTRAINT "TradeListings_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES "Player"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TradeListings" ADD CONSTRAINT "TradeListings_shardId_fkey" FOREIGN KEY ("shardId") REFERENCES "BlockchainShard"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Trade" ADD CONSTRAINT "Trade_buyerId_fkey" FOREIGN KEY ("buyerId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Trade" ADD CONSTRAINT "Trade_sellerId_fkey" FOREIGN KEY ("sellerId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Syndicate" ADD CONSTRAINT "Syndicate_leaderId_fkey" FOREIGN KEY ("leaderId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SyndicateMember" ADD CONSTRAINT "SyndicateMember_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SyndicateMember" ADD CONSTRAINT "SyndicateMember_syndicateId_fkey" FOREIGN KEY ("syndicateId") REFERENCES "Syndicate"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DAOProposal" ADD CONSTRAINT "DAOProposal_proposerId_fkey" FOREIGN KEY ("proposerId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DAOVote" ADD CONSTRAINT "DAOVote_proposalId_fkey" FOREIGN KEY ("proposalId") REFERENCES "DAOProposal"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DAOVote" ADD CONSTRAINT "DAOVote_voterId_fkey" FOREIGN KEY ("voterId") REFERENCES "Player"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
