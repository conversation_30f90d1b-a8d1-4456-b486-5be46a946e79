import { useEffect, useCallback } from 'react';
import { useGameStore } from '~/stores/game-store';

/**
 * Hook that manages passive income state tracking
 * Passive income is now handled server-side with notifications
 * This hook only tracks whether passive income should be active
 */
export function usePassiveIncome() {
  // Get stable references to store methods and data
  const startPassiveIncome = useGameStore(state => state.startPassiveIncome);
  const stopPassiveIncome = useGameStore(state => state.stopPassiveIncome);
  const miningRigs = useGameStore(state => state.miningRigs);

  // Memoize CPS calculation to avoid unnecessary re-renders
  const currentCPS = useCallback(() => {
    return miningRigs.reduce((total, rig) => {
      return total + (rig.baseOutput * rig.level * rig.quantity * rig.efficiency);
    }, 0);
  }, [miningRigs]);

  useEffect(() => {
    const cps = currentCPS();

    console.log(`🔄 Passive income effect triggered - CPS: ${cps}, Mining rigs: ${miningRigs.length}`);

    // Passive income is now handled server-side with notifications
    // We only need to track the CPS for display purposes
    if (cps > 0) {
      console.log(`✅ Passive income available with ${cps} CPS (handled server-side)`);
      startPassiveIncome();
    } else {
      console.log(`⏹️ No passive income (no mining rigs or 0 CPS)`);
      stopPassiveIncome();
    }

    // Cleanup on unmount or when CPS changes
    // No cleanup needed since we're not using intervals anymore
    return () => {};
  }, [currentCPS, startPassiveIncome, stopPassiveIncome, miningRigs]);

  // Stop passive income when component unmounts
  useEffect(() => {
    // No cleanup needed since we're not using intervals anymore
    return () => {};
  }, []);

  return {
    cps: currentCPS(),
  };
}