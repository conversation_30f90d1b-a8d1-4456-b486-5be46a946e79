import { describe, test, expect } from 'vitest';
import {
  calculateTotalCPS,
  calculateRigCost,
  calculateUpgradeCost,
  calculateOfflineProduction,
  calculateClickPower,
  applyShardBonuses,
  validateClickRate,
  validateClickPattern,
} from '../game-utils';
import { decimalToNumber, numberToDecimal } from '../game-utils-server';
import { RigType, ShardRarity, ShardCategory } from '../../types/game';
import type { MiningRig, PlayerShard, BlockchainShard } from '../../types/game';
import { Decimal } from '@prisma/client/runtime/library';
import { GAME_CONSTANTS } from '../../config/game-constants';

describe('Mining System Edge Cases and Advanced Scenarios', () => {
  describe('Rate Limiting Edge Cases', () => {
    test('should handle rapid clicking followed by normal clicking', () => {
      const now = Date.now();
      const clicks: number[] = [];
      
      // Rapid clicking for 500ms (very consistent intervals - bot-like)
      for (let i = 0; i < 10; i++) {
        clicks.push(now - 1000 + i * 50); // Exactly 50ms apart
      }
      
      // Normal clicking after
      for (let i = 0; i < 5; i++) {
        clicks.push(now - 500 + i * 100);
      }
      
      // Should pass rate limiting (15 clicks in 1 second window)
      expect(validateClickRate(clicks)).toBe(true);
      
      // The pattern validation should detect the consistent 50ms intervals in the last 10 clicks
      // However, since we mixed rapid and normal clicks, the variance might be high enough to pass
      // Let's test with purely consistent intervals instead
      const consistentClicks = Array.from({ length: 10 }, (_, i) => now - i * 50);
      expect(validateClickPattern(consistentClicks)).toBe(false);
    });

    test('should handle clicks at exact rate limit boundary', () => {
      const now = Date.now();
      const exactLimitClicks = Array.from(
        { length: GAME_CONSTANTS.CLICK_RATE_LIMIT }, 
        (_, i) => now - i * (GAME_CONSTANTS.CLICK_RATE_WINDOW / GAME_CONSTANTS.CLICK_RATE_LIMIT)
      );
      
      expect(validateClickRate(exactLimitClicks)).toBe(true);
      
      // One more click should fail
      exactLimitClicks.push(now);
      expect(validateClickRate(exactLimitClicks)).toBe(false);
    });

    test('should handle mixed valid and invalid timestamps', () => {
      const now = Date.now();
      const mixedClicks = [
        now - 100,
        NaN,
        now - 200,
        -1,
        now - 300,
        Infinity,
        now - 400,
      ];
      
      // Should filter out invalid timestamps and validate remaining
      expect(validateClickRate(mixedClicks as number[])).toBe(true);
    });
  });

  describe('Shard Effect Combinations', () => {
    test('should handle complex shard effect stacking with multiple targets', () => {
      const complexShards: BlockchainShard[] = [
        {
          id: 'shard1',
          name: 'Universal Boost',
          rarity: ShardRarity.LEGENDARY,
          category: ShardCategory.SPECIAL_ABILITY,
          effects: [
            { type: 'MULTIPLIER', target: 'everything', value: 1.1 },
            { type: 'FLAT_BONUS', target: 'mining_output', value: 5 },
          ],
          description: 'Boosts everything',
        },
        {
          id: 'shard2',
          name: 'Specific Boost',
          rarity: ShardRarity.RARE,
          category: ShardCategory.MINING_BOOST,
          effects: [
            { type: 'MULTIPLIER', target: 'mining_output', value: 1.2 },
            { type: 'MULTIPLIER', target: 'all_production', value: 1.05 },
          ],
          description: 'Specific mining boost',
        },
      ];

      const playerShards: PlayerShard[] = [
        {
          id: 'ps1',
          playerId: 'player1',
          shardId: 'shard1',
          shard: complexShards[0],
          quantity: 2,
          equipped: true,
          acquiredAt: new Date(),
        },
        {
          id: 'ps2',
          playerId: 'player1',
          shardId: 'shard2',
          shard: complexShards[1],
          quantity: 1,
          equipped: true,
          acquiredAt: new Date(),
        },
      ];

      const baseValue = 100;
      const result = applyShardBonuses(baseValue, playerShards, 'mining_output');
      
      // Base: 100
      // Flat bonus from shard1 (quantity 2): +5 * 2 = +10 → 110
      // Multipliers: everything (1.1^2) * mining_output (1.2) * all_production (1.05)
      // Result: 110 * 1.21 * 1.2 * 1.05 = 167.706
      expect(result).toBeCloseTo(167.706, 2);
    });

    test('should handle shard effects with conditions (placeholder for future)', () => {
      const conditionalShard: BlockchainShard = {
        id: 'shard1',
        name: 'Conditional Boost',
        rarity: ShardRarity.EPIC,
        category: ShardCategory.SPECIAL_ABILITY,
        effects: [
          { 
            type: 'MULTIPLIER', 
            target: 'mining_output', 
            value: 2.0,
            conditions: ['night_time', 'full_moon'] // Future feature
          },
        ],
        description: 'Conditional boost',
      };

      const playerShards: PlayerShard[] = [
        {
          id: 'ps1',
          playerId: 'player1',
          shardId: 'shard1',
          shard: conditionalShard,
          quantity: 1,
          equipped: true,
          acquiredAt: new Date(),
        },
      ];

      const baseValue = 100;
      // For now, conditions are not implemented, so effect should still apply
      const result = applyShardBonuses(baseValue, playerShards, 'mining_output');
      expect(result).toBe(200); // 100 * 2.0
    });
  });

  describe('Mining Rig Performance Edge Cases', () => {
    test('should handle rigs with zero or very low efficiency', () => {
      const lowEfficiencyRigs: MiningRig[] = [
        {
          id: '1',
          playerId: 'player1',
          rigType: RigType.USB_ASIC,
          level: 1,
          quantity: 1,
          baseOutput: new Decimal(0.1),
          efficiency: new Decimal(0.001), // Very low efficiency
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          playerId: 'player1',
          rigType: RigType.GPU_FARM,
          level: 1,
          quantity: 1,
          baseOutput: new Decimal(1.0),
          efficiency: new Decimal(0), // Zero efficiency
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const totalCPS = calculateTotalCPS(lowEfficiencyRigs);
      // First rig: 0.1 * 0.001 * 1 * 1 = 0.0001
      // Second rig: 1.0 * 0 * 1 * 1 = 0
      // Total: 0.0001
      expect(totalCPS).toBeCloseTo(0.0001, 6);
    });

    test('should handle very high level rigs without overflow', () => {
      const highLevelRig: MiningRig = {
        id: '1',
        playerId: 'player1',
        rigType: RigType.DYSON_SPHERE,
        level: 999, // Near maximum level
        quantity: 1000, // High quantity
        baseOutput: new Decimal(1400),
        efficiency: new Decimal(1.0),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const totalCPS = calculateTotalCPS([highLevelRig]);
      expect(totalCPS).toBe(1398600000); // 1400 * 1.0 * 999 * 1000
      expect(isFinite(totalCPS)).toBe(true);
      expect(totalCPS).toBeGreaterThan(0);
    });

    test('should handle fractional quantities and levels gracefully', () => {
      // This tests the robustness of the system with unexpected data
      const fractionalRig: MiningRig = {
        id: '1',
        playerId: 'player1',
        rigType: RigType.USB_ASIC,
        level: 1, // Integer level (as expected)
        quantity: 1, // Integer quantity (as expected)
        baseOutput: new Decimal(0.123456789), // High precision decimal
        efficiency: new Decimal(1.987654321), // High precision efficiency
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const totalCPS = calculateTotalCPS([fractionalRig]);
      const expected = 0.123456789 * 1.987654321 * 1 * 1;
      expect(totalCPS).toBeCloseTo(expected, 10);
    });
  });

  describe('Cost Calculation Edge Cases', () => {
    test('should handle cost calculations for large quantities', () => {
      const largeCost = calculateRigCost(RigType.USB_ASIC, 0, 100);
      
      // Should be finite and positive
      expect(isFinite(largeCost)).toBe(true);
      expect(largeCost).toBeGreaterThan(0);
      
      // Should be significantly larger than single rig cost
      const singleCost = calculateRigCost(RigType.USB_ASIC, 0, 1);
      expect(largeCost).toBeGreaterThan(singleCost * 100);
    });

    test('should handle upgrade costs for high levels', () => {
      const highLevelUpgradeCost = calculateUpgradeCost(RigType.DYSON_SPHERE, 500, 10);
      
      expect(isFinite(highLevelUpgradeCost)).toBe(true);
      expect(highLevelUpgradeCost).toBeGreaterThan(0);
      
      // Should be much more expensive than low level upgrades
      const lowLevelUpgradeCost = calculateUpgradeCost(RigType.USB_ASIC, 1, 10);
      expect(highLevelUpgradeCost).toBeGreaterThan(lowLevelUpgradeCost * 1000);
    });

    test('should handle zero quantity and level edge cases', () => {
      expect(calculateRigCost(RigType.USB_ASIC, 0, 0)).toBe(0);
      expect(calculateUpgradeCost(RigType.USB_ASIC, 1, 0)).toBe(0);
      
      // Negative values should still work (though not expected in normal gameplay)
      expect(calculateRigCost(RigType.USB_ASIC, 0, -1)).toBe(0);
      expect(calculateUpgradeCost(RigType.USB_ASIC, 1, -1)).toBe(0);
    });
  });

  describe('Offline Production Edge Cases', () => {
    test('should handle very long offline periods correctly', () => {
      const basicRig: MiningRig = {
        id: '1',
        playerId: 'player1',
        rigType: RigType.USB_ASIC,
        level: 1,
        quantity: 1,
        baseOutput: new Decimal(0.1),
        efficiency: new Decimal(1.0),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // 48 hours offline (should be capped at 24 hours)
      const veryLongOfflineTime = 48 * 3600;
      const production = calculateOfflineProduction([basicRig], [], veryLongOfflineTime);
      
      // Should be capped at 24 hours with efficiency penalty
      const maxHoursProduction = calculateOfflineProduction([basicRig], [], 24 * 3600);
      expect(production).toBe(maxHoursProduction);
    });

    test('should handle offline production with no rigs', () => {
      const production = calculateOfflineProduction([], [], 3600);
      expect(production).toBe(0);
    });

    test('should handle very short offline periods', () => {
      const basicRig: MiningRig = {
        id: '1',
        playerId: 'player1',
        rigType: RigType.USB_ASIC,
        level: 1,
        quantity: 1,
        baseOutput: new Decimal(0.1),
        efficiency: new Decimal(1.0),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // 1 second offline
      const shortOfflineTime = 1;
      const production = calculateOfflineProduction([basicRig], [], shortOfflineTime);
      
      // Should be very small but positive
      expect(production).toBeGreaterThan(0);
      expect(production).toBeLessThan(1);
    });
  });

  describe('Decimal Conversion Edge Cases', () => {
    test('should handle various decimal formats correctly', () => {
      // Test different input types
      expect(decimalToNumber(new Decimal('123.456'))).toBe(123.456);
      expect(decimalToNumber('789.123')).toBe(789.123);
      expect(decimalToNumber(456.789)).toBe(456.789);
      
      // Test edge cases
      expect(decimalToNumber(0)).toBe(0);
      expect(decimalToNumber('0')).toBe(0);
      expect(decimalToNumber(new Decimal(0))).toBe(0);
      
      // Test invalid inputs
      expect(decimalToNumber(null as any)).toBe(0);
      expect(decimalToNumber(undefined as any)).toBe(0);
      expect(decimalToNumber({} as any)).toBe(0);
    });

    test('should handle number to decimal conversion correctly', () => {
      const decimal = numberToDecimal(123.456);
      expect(decimal).toBeInstanceOf(Decimal);
      expect(decimal.toNumber()).toBe(123.456);
      
      // Test edge cases
      expect(numberToDecimal(0).toNumber()).toBe(0);
      expect(numberToDecimal(-123.456).toNumber()).toBe(-123.456);
      
      // Test very large numbers
      const largeNumber = 1e15;
      expect(numberToDecimal(largeNumber).toNumber()).toBe(largeNumber);
    });
  });

  describe('Game Constants Validation', () => {
    test('should have consistent and reasonable game constants', () => {
      // Rate limiting constants should be reasonable
      expect(GAME_CONSTANTS.CLICK_RATE_LIMIT).toBeGreaterThan(0);
      expect(GAME_CONSTANTS.CLICK_RATE_LIMIT).toBeLessThan(100);
      expect(GAME_CONSTANTS.CLICK_RATE_WINDOW).toBeGreaterThan(0);
      expect(GAME_CONSTANTS.CLICK_BURST_LIMIT).toBeGreaterThan(GAME_CONSTANTS.CLICK_RATE_LIMIT);
      
      // Offline production constants should be reasonable
      expect(GAME_CONSTANTS.MAX_OFFLINE_HOURS).toBeGreaterThan(0);
      expect(GAME_CONSTANTS.MAX_OFFLINE_HOURS).toBeLessThan(168); // Less than a week
      expect(GAME_CONSTANTS.OFFLINE_EFFICIENCY_THRESHOLD).toBeGreaterThan(0);
      expect(GAME_CONSTANTS.OFFLINE_EFFICIENCY_THRESHOLD).toBeLessThan(GAME_CONSTANTS.MAX_OFFLINE_HOURS);
      expect(GAME_CONSTANTS.OFFLINE_EFFICIENCY_PENALTY).toBeGreaterThan(0);
      expect(GAME_CONSTANTS.OFFLINE_EFFICIENCY_PENALTY).toBeLessThan(1);
      
      // Base values should be positive
      expect(GAME_CONSTANTS.BASE_CLICK_POWER).toBeGreaterThan(0);
    });
  });
});