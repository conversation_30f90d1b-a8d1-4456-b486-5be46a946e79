import { ChartBarIcon } from "@heroicons/react/24/outline";
import { GameLayout } from "~/app/_components/layout/GameLayout";
import { PlaceholderPage } from "~/app/_components/ui/PlaceholderPage";
import { auth } from "~/server/auth";
import { HydrateClient } from "~/trpc/server";
import { redirect } from "next/navigation";

export default async function MarketPage() {
	const session = await auth();

	if (!session?.user) {
		redirect("/api/auth/signin");
	}

	return (
		<HydrateClient>
			<GameLayout>
				<PlaceholderPage
					title="Cryptocurrency Market"
					description="Trade cryptocurrencies, monitor market trends, and maximize your profits in the dynamic crypto market."
					icon={ChartBarIcon}
				/>
			</GameLayout>
		</HydrateClient>
	);
}