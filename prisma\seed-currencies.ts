import { PrismaClient, CurrencyType } from '@prisma/client';
import { CURRENCY_CONFIGS } from '../src/config/game-constants';

const prisma = new PrismaClient();

async function seedCurrencies() {
  console.log('Seeding currency settings...');

  // Delete existing currency settings
  await prisma.currencySettings.deleteMany({});

  // Create currency settings for each currency in the config
  for (const [currencyType, config] of Object.entries(CURRENCY_CONFIGS)) {
    const currency = currencyType as CurrencyType;
    
    await prisma.currencySettings.create({
      data: {
        currency,
        name: config.name,
        symbol: config.symbol,
        color: config.color,
        description: config.description,
        baseValue: config.baseValue,
        isEnabled: config.isEnabled !== false,
        isUnlocked: currency === CurrencyType.CRYPTO_COIN, // Only CryptoCoin is unlocked by default
        unlockRequirement: config.unlockRequirement ? JSON.stringify(config.unlockRequirement) : null,
      },
    });
  }

  console.log('Currency settings seeded successfully!');
}

async function main() {
  try {
    await seedCurrencies();
  } catch (error) {
    console.error('Error seeding currencies:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();