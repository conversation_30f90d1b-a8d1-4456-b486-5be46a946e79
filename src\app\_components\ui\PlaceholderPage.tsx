"use client";

import { CpuChipIcon } from "@heroicons/react/24/outline";

interface PlaceholderPageProps {
  title: string;
  description: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export function PlaceholderPage({ 
  title, 
  description, 
  icon: Icon = CpuChipIcon 
}: PlaceholderPageProps) {
  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <div className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center">
          <Icon className="w-8 h-8 text-gray-400" />
        </div>
        <h1 className="text-2xl font-bold text-white">{title}</h1>
        <p className="text-gray-400 max-w-md">{description}</p>
        <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
          <p className="text-yellow-300 text-sm">
            🚧 This feature is coming soon! Stay tuned for updates.
          </p>
        </div>
      </div>
    </div>
  );
}