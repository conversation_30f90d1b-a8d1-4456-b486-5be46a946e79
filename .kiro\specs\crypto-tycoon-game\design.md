# Design Document

## Overview

Crypto Tycoon: The Blockchain Empire will be built as a modern web application using Next.js 15, React 19, tRPC, and Prisma with PostgreSQL. The architecture follows a full-stack TypeScript approach with real-time updates, server-side game logic, and a responsive dark-themed UI. The game implements a sophisticated economic simulation with player-driven markets, automated systems, and social features.

## Architecture

### Technology Stack
- **Frontend**: Next.js 15 with React 19, <PERSON><PERSON><PERSON><PERSON><PERSON> for styling
- **Backend**: tRPC for type-safe API, Next.js API routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js with Prisma adapter
- **Real-time**: Server-Sent Events (SSE) for live updates
- **State Management**: TanStack Query for server state, React hooks for client state
- **Validation**: Zod for runtime type validation

### System Architecture

```mermaid
graph TB
    Client[React Client] --> tRPC[tRPC Router]
    tRPC --> GameEngine[Game Engine Service]
    tRPC --> MarketService[Market Simulation Service]
    tRPC --> EventService[Blockchain Events Service]
    
    GameEngine --> Database[(PostgreSQL)]
    MarketService --> Database
    EventService --> Database
    
    GameEngine --> JobQueue[Background Jobs]
    MarketService --> JobQueue
    EventService --> JobQueue
    
    JobQueue --> OfflineCalculation[Offline Production]
    JobQueue --> MarketUpdates[Market Price Updates]
    JobQueue --> EventTriggers[Event System]
    
    Client --> SSE[Server-Sent Events]
    SSE --> RealTimeUpdates[Real-time Updates]
```

### Core Services Architecture

1. **Game Engine Service**: Handles core game mechanics, mining calculations, and player progression
2. **Market Simulation Service**: Manages cryptocurrency prices, trading, and market events
3. **Event System Service**: Orchestrates blockchain events and their effects
4. **Shard Management Service**: Handles Blockchain Shard drops, trading, and effects
5. **Social Features Service**: Manages Syndicates, DAO governance, and player interactions

## Components and Interfaces

### Frontend Components

#### Layout Components
- `GameLayout`: Main game container with sidebar navigation
- `Sidebar`: Navigation menu with sections (Dashboard, Mining, Market, etc.)
- `Header`: Top bar with player stats and notifications
- `NotificationSystem`: Toast notifications for events and achievements

#### Game Feature Components
- `Dashboard`: Main overview with key metrics and quick actions
- `MiningPanel`: Mining rig management and click interface
- `MarketExchange`: Cryptocurrency trading interface
- `ResearchTree`: R&D progression visualization
- `DataCenterMap`: Interactive location selection
- `ShardInventory`: Blockchain Shard collection and management
- `DEXMarketplace`: Player-to-player trading interface
- `SyndicateHub`: Guild management and activities
- `DAOGovernance`: Voting and proposal interface

#### UI Components
- `CurrencyDisplay`: Formatted cryptocurrency amounts
- `ProgressBar`: Visual progress indicators
- `MarketChart`: Real-time price visualization
- `ShardCard`: Individual shard display with effects
- `RigCard`: Mining rig information and controls
- `EventNotification`: Blockchain event announcements

### Backend API Structure

#### tRPC Routers

```typescript
// Main router structure
export const appRouter = router({
  game: gameRouter,
  market: marketRouter,
  shards: shardRouter,
  social: socialRouter,
  events: eventRouter,
});

// Game router endpoints
gameRouter = router({
  getPlayerState: publicProcedure.query(),
  clickMine: protectedProcedure.mutation(),
  purchaseRig: protectedProcedure.input(purchaseRigSchema).mutation(),
  upgradeRig: protectedProcedure.input(upgradeRigSchema).mutation(),
  switchMiningTarget: protectedProcedure.input(switchTargetSchema).mutation(),
  calculateOfflineProduction: protectedProcedure.mutation(),
});
```

### Database Schema Design

#### Core Game Tables

```sql
-- Player game state
model Player {
  id                String   @id @default(cuid())
  userId            String   @unique
  user              User     @relation(fields: [userId], references: [id])
  
  -- Core currencies
  cryptoCoin        Decimal  @default(0)
  ethereumG         Decimal  @default(0)
  byteCoin          Decimal  @default(0)
  solanaX           Decimal  @default(0)
  
  -- Game progression
  totalClicks       BigInt   @default(0)
  prestigeLevel     Int      @default(0)
  blockchainPoints  Int      @default(0)
  
  -- Timestamps
  lastActiveAt      DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  -- Relations
  miningRigs        MiningRig[]
  shards           PlayerShard[]
  research         PlayerResearch[]
  dataCenters      PlayerDataCenter[]
  trades           Trade[]
  syndicateMember  SyndicateMember?
}

-- Mining equipment
model MiningRig {
  id          String   @id @default(cuid())
  playerId    String
  player      Player   @relation(fields: [playerId], references: [id])
  
  rigType     RigType
  level       Int      @default(1)
  quantity    Int      @default(1)
  
  -- Performance stats
  baseOutput  Decimal
  efficiency  Decimal  @default(1.0)
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

-- Blockchain Shards
model BlockchainShard {
  id          String      @id @default(cuid())
  name        String
  rarity      ShardRarity
  category    ShardCategory
  effects     Json        -- Stored as JSON for flexibility
  description String
  
  playerShards PlayerShard[]
  tradeListings TradeListings[]
}

model PlayerShard {
  id       String @id @default(cuid())
  playerId String
  player   Player @relation(fields: [playerId], references: [id])
  shardId  String
  shard    BlockchainShard @relation(fields: [shardId], references: [id])
  
  quantity Int @default(1)
  equipped Boolean @default(false)
  
  acquiredAt DateTime @default(now())
}
```

#### Market and Trading Tables

```sql
-- Cryptocurrency market data
model CryptocurrencyPrice {
  id           String          @id @default(cuid())
  currency     CurrencyType
  price        Decimal
  volume24h    Decimal
  volatility   Decimal
  
  timestamp    DateTime        @default(now())
}

-- Player-to-player trading
model TradeListings {
  id          String   @id @default(cuid())
  sellerId    String
  seller      Player   @relation(fields: [sellerId], references: [id])
  shardId     String
  shard       BlockchainShard @relation(fields: [shardId], references: [id])
  
  price       Decimal
  currency    CurrencyType
  quantity    Int
  
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  expiresAt   DateTime?
}

model Trade {
  id          String   @id @default(cuid())
  buyerId     String
  buyer       Player   @relation("BuyerTrades", fields: [buyerId], references: [id])
  sellerId    String
  seller      Player   @relation("SellerTrades", fields: [sellerId], references: [id])
  
  shardId     String
  price       Decimal
  currency    CurrencyType
  quantity    Int
  
  completedAt DateTime @default(now())
}
```

#### Social Features Tables

```sql
-- Guild system
model Syndicate {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  leaderId    String
  leader      Player   @relation(fields: [leaderId], references: [id])
  
  level       Int      @default(1)
  experience  BigInt   @default(0)
  
  members     SyndicateMember[]
  createdAt   DateTime @default(now())
}

model SyndicateMember {
  id          String    @id @default(cuid())
  playerId    String    @unique
  player      Player    @relation(fields: [playerId], references: [id])
  syndicateId String
  syndicate   Syndicate @relation(fields: [syndicateId], references: [id])
  
  role        SyndicateRole @default(MEMBER)
  joinedAt    DateTime      @default(now())
  contribution BigInt       @default(0)
}

-- DAO Governance
model DAOProposal {
  id          String   @id @default(cuid())
  title       String
  description String
  proposerId  String
  proposer    Player   @relation(fields: [proposerId], references: [id])
  
  votesFor    Int      @default(0)
  votesAgainst Int     @default(0)
  
  status      ProposalStatus @default(ACTIVE)
  createdAt   DateTime @default(now())
  expiresAt   DateTime
  
  votes       DAOVote[]
}

model DAOVote {
  id         String      @id @default(cuid())
  proposalId String
  proposal   DAOProposal @relation(fields: [proposalId], references: [id])
  voterId    String
  voter      Player      @relation(fields: [voterId], references: [id])
  
  vote       VoteChoice
  weight     Int         -- Voting power based on holdings/shards
  castAt     DateTime    @default(now())
  
  @@unique([proposalId, voterId])
}
```

## Data Models

### Core Game State Models

```typescript
// Player state interface
interface PlayerState {
  id: string;
  currencies: {
    cryptoCoin: number;
    ethereumG: number;
    byteCoin: number;
    solanaX: number;
    // ... other currencies
  };
  stats: {
    totalClicks: number;
    cpsRate: number; // Coins per second
    prestigeLevel: number;
    blockchainPoints: number;
  };
  miningRigs: MiningRig[];
  shards: PlayerShard[];
  research: ResearchProgress[];
  dataCenters: DataCenterStatus[];
}

// Mining rig model
interface MiningRig {
  id: string;
  type: RigType;
  level: number;
  quantity: number;
  baseOutput: number;
  efficiency: number;
  totalOutput: number; // Calculated field
}

// Blockchain Shard model
interface BlockchainShard {
  id: string;
  name: string;
  rarity: 'COMMON' | 'UNCOMMON' | 'RARE' | 'EPIC' | 'LEGENDARY' | 'MYTHIC';
  category: ShardCategory;
  effects: ShardEffect[];
  description: string;
  marketValue?: number;
}

interface ShardEffect {
  type: 'MULTIPLIER' | 'FLAT_BONUS' | 'SPECIAL_ABILITY';
  target: string; // e.g., 'click_power', 'mining_output', 'cost_reduction'
  value: number;
  conditions?: string[]; // Optional conditions for effect activation
}
```

### Market System Models

```typescript
// Market data structure
interface MarketData {
  currencies: {
    [key in CurrencyType]: {
      price: number;
      change24h: number;
      volume: number;
      volatility: number;
    };
  };
  trends: MarketTrend[];
  events: ActiveMarketEvent[];
}

// Blockchain event model
interface BlockchainEvent {
  id: string;
  type: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  name: string;
  description: string;
  effects: EventEffect[];
  duration: number; // in seconds
  probability: number;
  conditions?: string[]; // Conditions for event to trigger
}

interface EventEffect {
  target: string; // What the event affects
  modifier: number; // Multiplier or flat change
  duration: number; // How long the effect lasts
}
```

## Error Handling

### Client-Side Error Handling

```typescript
// Error boundary for game components
class GameErrorBoundary extends React.Component {
  // Handle React errors gracefully
  // Show fallback UI for game sections
  // Log errors for debugging
}

// tRPC error handling
const utils = api.useUtils();
const mutation = api.game.clickMine.useMutation({
  onError: (error) => {
    if (error.code === 'UNAUTHORIZED') {
      // Redirect to login
    } else if (error.code === 'RATE_LIMITED') {
      // Show rate limit message
    } else {
      // Show generic error
    }
  },
  onSuccess: () => {
    utils.game.getPlayerState.invalidate();
  },
});
```

### Server-Side Error Handling

```typescript
// Custom error types
class GameError extends Error {
  code: string;
  statusCode: number;
  
  constructor(message: string, code: string, statusCode = 400) {
    super(message);
    this.code = code;
    this.statusCode = statusCode;
  }
}

// Error handling middleware
const errorHandler = (error: unknown) => {
  if (error instanceof GameError) {
    return {
      code: error.code,
      message: error.message,
    };
  }
  
  // Log unexpected errors
  console.error('Unexpected error:', error);
  return {
    code: 'INTERNAL_ERROR',
    message: 'An unexpected error occurred',
  };
};
```

### Database Error Handling

- **Connection failures**: Implement retry logic with exponential backoff
- **Transaction conflicts**: Handle concurrent updates with optimistic locking
- **Data validation**: Use Zod schemas for runtime validation
- **Migration errors**: Implement rollback procedures for schema changes

## Testing Strategy

### Unit Testing

```typescript
// Game logic testing
describe('Mining System', () => {
  test('should calculate correct CPS from mining rigs', () => {
    const rigs = [
      { type: 'USB_ASIC', level: 1, quantity: 5, baseOutput: 0.1 },
      { type: 'GPU_FARM', level: 2, quantity: 2, baseOutput: 5.0 },
    ];
    
    const totalCPS = calculateTotalCPS(rigs);
    expect(totalCPS).toBe(10.5); // (0.1 * 5) + (5.0 * 2)
  });
  
  test('should apply shard bonuses correctly', () => {
    const baseOutput = 100;
    const shards = [
      { effects: [{ type: 'MULTIPLIER', target: 'mining_output', value: 1.1 }] },
      { effects: [{ type: 'FLAT_BONUS', target: 'mining_output', value: 10 }] },
    ];
    
    const finalOutput = applyShardBonuses(baseOutput, shards);
    expect(finalOutput).toBe(121); // (100 + 10) * 1.1
  });
});
```

### Integration Testing

```typescript
// API endpoint testing
describe('Game API', () => {
  test('should handle click mining with rate limiting', async () => {
    const { req, res } = createMocks({ method: 'POST' });
    
    // Test multiple rapid clicks
    for (let i = 0; i < 10; i++) {
      await clickMineHandler(req, res);
    }
    
    // Should be rate limited after threshold
    expect(res._getStatusCode()).toBe(429);
  });
});
```

### End-to-End Testing

```typescript
// User journey testing
describe('Player Progression', () => {
  test('should allow complete mining rig purchase flow', async () => {
    // 1. Start with initial CTC balance
    // 2. Click mine to earn more CTC
    // 3. Purchase first mining rig
    // 4. Verify automatic CTC generation
    // 5. Upgrade rig and verify increased output
  });
});
```

### Performance Testing

- **Load testing**: Simulate concurrent users for market updates
- **Database performance**: Test query performance with large datasets
- **Real-time updates**: Verify SSE performance under load
- **Memory usage**: Monitor for memory leaks in long-running sessions

### Security Testing

- **Authentication**: Verify proper session management
- **Authorization**: Test access controls for player data
- **Input validation**: Prevent injection attacks and data manipulation
- **Rate limiting**: Ensure anti-cheat measures are effective

## Real-Time System Design

### Server-Sent Events Implementation

```typescript
// SSE endpoint for real-time updates
export async function GET(request: Request) {
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      
      // Send periodic updates
      const interval = setInterval(() => {
        const data = {
          type: 'MARKET_UPDATE',
          payload: getCurrentMarketData(),
        };
        
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(data)}\n\n`)
        );
      }, 5000);
      
      // Cleanup on close
      request.signal.addEventListener('abort', () => {
        clearInterval(interval);
        controller.close();
      });
    },
  });
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}
```

### Background Job Processing

```typescript
// Offline production calculation
export async function calculateOfflineProduction(playerId: string) {
  const player = await db.player.findUnique({
    where: { id: playerId },
    include: { miningRigs: true, shards: true },
  });
  
  const offlineTime = Date.now() - player.lastActiveAt.getTime();
  const offlineHours = offlineTime / (1000 * 60 * 60);
  
  // Calculate production with diminishing returns for long offline periods
  const maxOfflineHours = 24;
  const effectiveHours = Math.min(offlineHours, maxOfflineHours);
  const efficiency = effectiveHours <= 8 ? 1.0 : 0.8; // Reduced efficiency after 8 hours
  
  const totalProduction = calculateTotalCPS(player.miningRigs) * effectiveHours * 3600 * efficiency;
  
  await db.player.update({
    where: { id: playerId },
    data: {
      cryptoCoin: { increment: totalProduction },
      lastActiveAt: new Date(),
    },
  });
  
  return totalProduction;
}
```

This design provides a solid foundation for building the crypto tycoon game with scalable architecture, comprehensive error handling, and robust testing strategies. The system is designed to handle the complex interactions between mining, market simulation, social features, and real-time updates while maintaining performance and security.