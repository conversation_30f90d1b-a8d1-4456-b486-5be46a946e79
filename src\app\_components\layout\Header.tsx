"use client";

import { BellIcon, UserCircleIcon } from "@heroicons/react/24/outline";
import { CurrencyDisplay } from "../ui/CurrencyDisplay";
import { CurrencyType } from "~/types/game";
import { useGameStore } from "~/stores/game-store";

interface HeaderProps {
  onMenuToggle: () => void;
}

export function Header({ onMenuToggle }: HeaderProps) {
  // Use the game store to get real-time balance updates
  const player = useGameStore(state => state.player);
  const getCurrentBalance = useGameStore(state => state.getCurrentBalance);
  const getTotalCPS = useGameStore(state => state.getTotalCPS);

  return (
    <header className="bg-gray-800/50 backdrop-blur-sm border-b border-gray-700/50 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Player stats */}
        <div className="flex items-center space-x-6">
          {player && (
            <>
              <CurrencyDisplay
                amount={getCurrentBalance()}
                currency={CurrencyType.CRYPTO_COIN}
                showIcon
              />
              <CurrencyDisplay
                amount={player.currencies.ethereumG}
                currency={CurrencyType.ETHEREUM_G}
                showIcon
              />
              <div className="text-sm text-gray-400">
                CPS: <span className="text-green-400 font-medium">
                  {getTotalCPS().toFixed(2)}
                </span>
              </div>
            </>
          )}
        </div>

        {/* Right side - User actions */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <button className="relative p-2 rounded-lg hover:bg-gray-700/50 transition-colors">
            <BellIcon className="w-5 h-5 text-gray-300" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
          </button>

          {/* User menu */}
          <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-700/50 transition-colors">
            <UserCircleIcon className="w-6 h-6 text-gray-300" />
            <span className="text-sm text-gray-300">Player</span>
          </button>
        </div>
      </div>
    </header>
  );
}