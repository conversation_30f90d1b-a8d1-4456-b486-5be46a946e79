import { CubeIcon } from "@heroicons/react/24/outline";
import { GameLayout } from "~/app/_components/layout/GameLayout";
import { PlaceholderPage } from "~/app/_components/ui/PlaceholderPage";
import { auth } from "~/server/auth";
import { HydrateClient } from "~/trpc/server";
import { redirect } from "next/navigation";

export default async function ShardsPage() {
	const session = await auth();

	if (!session?.user) {
		redirect("/api/auth/signin");
	}

	return (
		<HydrateClient>
			<GameLayout>
				<PlaceholderPage
					title="Blockchain Shards"
					description="Collect rare Blockchain Shards that provide powerful bonuses and unique effects to enhance your mining operations."
					icon={CubeIcon}
				/>
			</GameLayout>
		</HydrateClient>
	);
}