import React from 'react';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { api } from '~/trpc/react';
import type { CurrencyType } from '~/types/game';

interface PlayerState {
  id: string;
  userId: string;
  currencies: {
    cryptoCoin: number;
    ethereumG: number;
    byteCoin: number;
    solanaX: number;
    cardanoZ: number;
    polkadotY: number;
    avalancheA?: number;
    cosmosC?: number;
    tezosT?: number;
    algorandAlgo?: number;
  };
  stats: {
    totalClicks: number;
    prestigeLevel: number;
    blockchainPoints: number;
    cpsRate: number;
  };
  timestamps: {
    lastActiveAt: Date;
    createdAt: Date;
    updatedAt: Date;
  };
  activeMiningCurrency: CurrencyType;
}

interface MiningRig {
  id: string;
  rigType: string;
  level: number;
  quantity: number;
  baseOutput: number;
  efficiency: number;
}

interface GameState {
  // State
  player: PlayerState | null;
  miningRigs: MiningRig[];
  isLoading: boolean;
  lastFetch: number;
  
  // Optimistic state
  pendingClicks: number;
  optimisticBalance: number;
  
  // Passive income simulation
  lastPassiveUpdate: number;
  passiveIncomeActive: boolean;
  lastDebugLog?: number;
  
  // Market data
  marketData: {
    [key in CurrencyType]?: {
      price: number;
      change24h: number;
      volume: number;
      volatility: number;
      name: string;
      symbol: string;
      color: string;
      isUnlocked: boolean;
      isEnabled: boolean;
    };
  };
  
  // Actions
  setPlayerState: (data: { player: PlayerState; miningRigs: MiningRig[] }) => void;
  addOptimisticClick: (amount: number) => void;
  clearOptimisticClicks: () => void;
  updateFromServer: (data: { player: PlayerState; miningRigs: MiningRig[] }) => void;
  forceServerSync: (data: { player: PlayerState; miningRigs: MiningRig[] }) => void;
  startPassiveIncome: () => void;
  stopPassiveIncome: () => void;
  updatePassiveIncome: () => void;
  updateMarketData: (data: any) => void;
  
  // Computed values
  getTotalCPS: () => number;
  getCurrentBalance: () => number;
  getActiveMiningCurrency: () => CurrencyType;
  getCurrencyBalance: (currency: CurrencyType) => number;
}

export const useGameStore = create<GameState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    player: null,
    miningRigs: [],
    isLoading: false,
    lastFetch: 0,
    pendingClicks: 0,
    optimisticBalance: 0,
    lastPassiveUpdate: Date.now(),
    passiveIncomeActive: false,
    marketData: {},

    // Actions
    setPlayerState: (data) => {
      set({
        player: data.player,
        miningRigs: data.miningRigs,
        lastFetch: Date.now(),
        optimisticBalance: data.player.currencies.cryptoCoin,
        lastPassiveUpdate: Date.now(),
      });
    },

    addOptimisticClick: (amount) => {
      set((state) => ({
        pendingClicks: state.pendingClicks + 1,
        optimisticBalance: state.optimisticBalance + amount,
      }));
    },

    clearOptimisticClicks: () => {
      set({ pendingClicks: 0 });
    },

    updateFromServer: (data) => {
      set((state) => {
        // Get the active mining currency
        const activeCurrency = data.player.activeMiningCurrency || CurrencyType.CRYPTO_COIN;
        
        // Get the balance for the active currency
        const serverBalance = data.player.currencies[activeCurrency.toLowerCase() as keyof typeof data.player.currencies] || 0;
        const balanceDiff = Math.abs(serverBalance - state.optimisticBalance);
        const timeSinceLastFetch = Date.now() - state.lastFetch;

        // Be more conservative about resetting optimistic balance
        // Only reset if:
        // 1. The difference is very large (>5 units), OR
        // 2. No pending clicks AND difference > 1 unit, OR
        // 3. It's been more than 30 seconds since last fetch
        const shouldResetOptimistic =
          balanceDiff > 5.0 ||
          (state.pendingClicks === 0 && balanceDiff > 1.0) ||
          timeSinceLastFetch > 30000;

        console.log(`📊 Server update - Server: ${serverBalance}, Optimistic: ${state.optimisticBalance}, Diff: ${balanceDiff.toFixed(2)}, Reset: ${shouldResetOptimistic}`);

        return {
          player: data.player,
          miningRigs: data.miningRigs,
          lastFetch: Date.now(),
          // Reset optimistic balance to server value only if there's a significant difference
          optimisticBalance: shouldResetOptimistic ? serverBalance : state.optimisticBalance,
          pendingClicks: shouldResetOptimistic ? 0 : state.pendingClicks,
          lastPassiveUpdate: Date.now(),
        };
      });
    },

    // Force sync with server (for when we know server is authoritative)
    forceServerSync: (data) => {
      const currentOptimistic = get().optimisticBalance;
      const activeCurrency = data.player.activeMiningCurrency || CurrencyType.CRYPTO_COIN;
      const newBalance = data.player.currencies[activeCurrency.toLowerCase() as keyof typeof data.player.currencies] || 0;
      
      console.log(`🔄 Force server sync - Old optimistic: ${currentOptimistic}, New balance: ${newBalance}, Diff: ${(newBalance - currentOptimistic).toFixed(2)}`);

      set({
        player: data.player,
        miningRigs: data.miningRigs,
        lastFetch: Date.now(),
        optimisticBalance: newBalance,
        pendingClicks: 0,
        lastPassiveUpdate: Date.now(), // Reset passive update timer to avoid conflicts
      });
    },

    // Passive income simulation
    startPassiveIncome: () => {
      const state = get();
      if (state.passiveIncomeActive) return;

      set({ passiveIncomeActive: true, lastPassiveUpdate: Date.now() });
    },

    stopPassiveIncome: () => {
      set({ passiveIncomeActive: false });
    },

    updatePassiveIncome: () => {
      const state = get();
      if (!state.passiveIncomeActive || !state.player) return;

      const now = Date.now();
      const timeDelta = (now - state.lastPassiveUpdate) / 1000; // Convert to seconds
      const cps = state.getTotalCPS();
      const activeCurrency = state.player.activeMiningCurrency || CurrencyType.CRYPTO_COIN;

      if (cps > 0 && timeDelta > 0) {
        // Apply currency-specific mining efficiency
        const currencyConfig = CURRENCY_CONFIGS[activeCurrency];
        const miningEfficiency = currencyConfig?.miningEfficiency || 1.0;
        const passiveEarnings = cps * timeDelta * miningEfficiency;
        const newBalance = state.optimisticBalance + passiveEarnings;
        const currencySymbol = currencyConfig?.symbol || 'CTC';

        // Debug logging (only log every 5 seconds to avoid spam)
        if (now - (state.lastDebugLog || 0) > 5000) {
          console.log(`💰 Passive income update: +${passiveEarnings.toFixed(4)} ${currencySymbol} (${cps} CPS × ${timeDelta.toFixed(2)}s × ${miningEfficiency} efficiency) = ${newBalance.toFixed(2)} total`);
          set({
            optimisticBalance: newBalance,
            lastPassiveUpdate: now,
            lastDebugLog: now,
          });
        } else {
          set({
            optimisticBalance: newBalance,
            lastPassiveUpdate: now,
          });
        }
      } else {
        set({ lastPassiveUpdate: now });
      }
    },

    // Update market data
    updateMarketData: (data) => {
      set({ marketData: data.currencies || {} });
    },

    // Computed values
    getTotalCPS: () => {
      const { miningRigs } = get();
      return miningRigs.reduce((total, rig) => {
        // Use proper calculation: baseOutput * level * quantity * efficiency
        return total + (rig.baseOutput * rig.level * rig.quantity * rig.efficiency);
      }, 0);
    },

    getCurrentBalance: () => {
      const { player, optimisticBalance } = get();
      return player ? optimisticBalance : 0;
    },

    getActiveMiningCurrency: () => {
      const { player } = get();
      return player?.activeMiningCurrency || CurrencyType.CRYPTO_COIN;
    },

    getCurrencyBalance: (currency) => {
      const { player } = get();
      if (!player) return 0;
      
      const currencyKey = currency.toLowerCase() as keyof typeof player.currencies;
      return player.currencies[currencyKey] || 0;
    },
  }))
);

// Hook for managing game data fetching
export function useGameData() {
  const store = useGameStore();
  const setPlayerState = useGameStore(state => state.setPlayerState);
  const updateMarketData = useGameStore(state => state.updateMarketData);
  
  const { data, refetch, isLoading } = api.game.getPlayerState.useQuery(
    undefined,
    {
      refetchInterval: false, // We'll handle this manually
      refetchOnWindowFocus: false,
    }
  );

  // Fetch market data
  const { data: marketData, refetch: refetchMarket } = api.game.getMarketData.useQuery(
    undefined,
    {
      refetchInterval: 30000, // Refresh market data every 30 seconds
      refetchOnWindowFocus: true,
    }
  );

  // Update store when player data changes
  React.useEffect(() => {
    if (data) {
      // Convert Decimal types to numbers for the store
      const convertedData = {
        player: {
          ...data.player,
          currencies: {
            cryptoCoin: Number(data.player.currencies.cryptoCoin),
            ethereumG: Number(data.player.currencies.ethereumG),
            byteCoin: Number(data.player.currencies.byteCoin),
            solanaX: Number(data.player.currencies.solanaX),
            cardanoZ: Number(data.player.currencies.cardanoZ),
            polkadotY: Number(data.player.currencies.polkadotY),
          },
          stats: {
            totalClicks: Number(data.player.totalClicks || 0),
            prestigeLevel: data.player.prestigeLevel || 0,
            blockchainPoints: data.player.blockchainPoints || 0,
            cpsRate: 0, // Will be calculated separately
          },
          activeMiningCurrency: data.player.activeMiningCurrency || CurrencyType.CRYPTO_COIN,
        },
        miningRigs: data.miningRigs.map(rig => ({
          ...rig,
          baseOutput: Number(rig.baseOutput),
          efficiency: Number(rig.efficiency),
        })),
      };
      setPlayerState(convertedData);
    }
  }, [data, setPlayerState]);

  // Update market data when it changes
  React.useEffect(() => {
    if (marketData) {
      updateMarketData(marketData);
    }
  }, [marketData, updateMarketData]);

  // Combined refetch function
  const refetchAll = React.useCallback(() => {
    refetch();
    refetchMarket();
  }, [refetch, refetchMarket]);

  return {
    ...store,
    refetch: refetchAll,
    refetchMarket,
    isLoading: isLoading || store.isLoading,
    marketData: marketData || { currencies: {}, trends: [], events: [] },
  };
}