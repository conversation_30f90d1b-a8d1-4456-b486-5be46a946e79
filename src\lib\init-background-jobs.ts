import { backgroundJobScheduler } from './background-jobs';

// Use a global variable to track initialization across hot reloads
declare global {
  var __backgroundJobsInitialized: boolean | undefined;
}

// Initialize background jobs when the server starts
export function initializeBackgroundJobs() {
  if (!global.__backgroundJobsInitialized && process.env.NODE_ENV !== 'test') {
    console.log('🚀 Initializing background job scheduler...');
    backgroundJobScheduler.start();
    global.__backgroundJobsInitialized = true;
    console.log('✅ Background job scheduler initialized successfully');

    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log('Shutting down background job scheduler...');
      backgroundJobScheduler.stop();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      console.log('Shutting down background job scheduler...');
      backgroundJobScheduler.stop();
      process.exit(0);
    });
  } else if (global.__backgroundJobsInitialized) {
    console.log('⚠️ Background jobs already initialized, skipping...');
  } else {
    console.log('⚠️ Skipping background job initialization (test environment)');
  }
}