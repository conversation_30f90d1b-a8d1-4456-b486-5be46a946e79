/**
 * Global notification system utilities
 */

export interface NotificationData {
  type: 'success' | 'error' | 'warning' | 'info' | 'passive_income';
  title: string;
  message: string;
  duration?: number;
  amount?: number; // For passive income notifications
}

/**
 * Show a notification globally
 */
export function showNotification(notification: NotificationData) {
  if (typeof window !== 'undefined') {
    const event = new CustomEvent('show_notification', {
      detail: notification
    });
    window.dispatchEvent(event);
  }
}

/**
 * Show a passive income notification
 */
export function showPassiveIncomeNotification(amount: number) {
  showNotification({
    type: 'passive_income',
    title: 'Passive Income',
    message: 'Your mining rigs generated coins!',
    duration: 3000, // 3 seconds
    amount,
  });
}

/**
 * Show a success notification
 */
export function showSuccessNotification(title: string, message: string, duration = 5000) {
  showNotification({
    type: 'success',
    title,
    message,
    duration,
  });
}

/**
 * Show an error notification
 */
export function showErrorNotification(title: string, message: string, duration = 5000) {
  showNotification({
    type: 'error',
    title,
    message,
    duration,
  });
}

/**
 * Show a warning notification
 */
export function showWarningNotification(title: string, message: string, duration = 5000) {
  showNotification({
    type: 'warning',
    title,
    message,
    duration,
  });
}

/**
 * Show an info notification
 */
export function showInfoNotification(title: string, message: string, duration = 5000) {
  showNotification({
    type: 'info',
    title,
    message,
    duration,
  });
}
