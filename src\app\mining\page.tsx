import { GameLayout } from "~/app/_components/layout/GameLayout";
import { MiningPanel } from "~/app/_components/game/MiningPanel";
import { auth } from "~/server/auth";
import { HydrateClient } from "~/trpc/server";
import { redirect } from "next/navigation";

export default async function MiningPage() {
	const session = await auth();

	if (!session?.user) {
		redirect("/api/auth/signin");
	}

	return (
		<HydrateClient>
			<GameLayout>
				<MiningPanel />
			</GameLayout>
		</HydrateClient>
	);
}