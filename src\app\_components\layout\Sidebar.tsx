"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  HomeIcon,
  CpuChipIcon,
  ChartBarIcon,
  BeakerIcon,
  MapIcon,
  CubeIcon,
  ArrowsRightLeftIcon,
  UserGroupIcon,
  ScaleIcon,
  PuzzlePieceIcon,
  Bars3Icon,
  XMarkIcon,
} from "@heroicons/react/24/outline";

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

const navigation = [
  { name: "Dashboard", href: "/", icon: HomeIcon },
  { name: "Mining", href: "/mining", icon: CpuChipIcon },
  { name: "Market", href: "/market", icon: ChartBarIcon },
  { name: "Research", href: "/research", icon: BeakerIcon },
  { name: "Data Centers", href: "/locations", icon: MapIcon },
  { name: "Shards", href: "/shards", icon: CubeIcon },
  { name: "Trading", href: "/trading", icon: ArrowsRightLeftIcon },
  { name: "Syndicates", href: "/syndicates", icon: UserGroupIcon },
  { name: "DAO", href: "/dao", icon: ScaleIcon },
  { name: "Mini-Games", href: "/minigames", icon: PuzzlePieceIcon },
];

export function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const pathname = usePathname();

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed left-0 top-0 z-50 h-full bg-gray-800/95 backdrop-blur-sm border-r border-gray-700/50
        transition-all duration-300 ease-in-out
        ${isOpen ? 'w-64' : 'w-16'}
        ${!isOpen && 'lg:w-16'}
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700/50">
          {isOpen && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                <CpuChipIcon className="w-5 h-5 text-white" />
              </div>
              <span className="font-bold text-lg bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                CryptoTycoon
              </span>
            </div>
          )}
          <button
            onClick={onToggle}
            className="p-2 rounded-lg hover:bg-gray-700/50 transition-colors"
          >
            {isOpen ? (
              <XMarkIcon className="w-5 h-5" />
            ) : (
              <Bars3Icon className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-6 px-3">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`
                      flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200
                      ${isActive 
                        ? 'bg-gradient-to-r from-purple-500/20 to-blue-500/20 text-purple-300 border border-purple-500/30' 
                        : 'text-gray-300 hover:bg-gray-700/50 hover:text-white'
                      }
                    `}
                  >
                    <item.icon className={`w-5 h-5 ${isOpen ? 'mr-3' : 'mx-auto'}`} />
                    {isOpen && (
                      <span className="truncate">{item.name}</span>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Footer */}
        {isOpen && (
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-700/50">
            <div className="text-xs text-gray-400 text-center">
              <div>Crypto Tycoon v1.0</div>
              <div className="mt-1">Build your blockchain empire</div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}