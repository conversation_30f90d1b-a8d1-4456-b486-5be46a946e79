import type { Decimal } from "@prisma/client/runtime/library";

// Core Game Types
export interface PlayerState {
  id: string;
  userId: string;
  currencies: {
    cryptoCoin: Decimal;
    ethereumG: Decimal;
    byteCoin: Decimal;
    solanaX: Decimal;
    cardanoZ: Decimal;
    polkadotY: Decimal;
    avalancheA?: Decimal;
    cosmosC?: Decimal;
    tezosT?: Decimal;
    algorandAlgo?: Decimal;
  };
  stats: {
    totalClicks: bigint;
    prestigeLevel: number;
    blockchainPoints: number;
  };
  timestamps: {
    lastActiveAt: Date;
    createdAt: Date;
    updatedAt: Date;
  };
  activeMiningCurrency?: CurrencyType;
}

export interface MiningRig {
  id: string;
  playerId: string;
  rigType: RigType;
  level: number;
  quantity: number;
  baseOutput: Decimal;
  efficiency: Decimal;
  createdAt: Date;
  updatedAt: Date;
}

export interface BlockchainShard {
  id: string;
  name: string;
  rarity: ShardRarity;
  category: ShardCategory;
  effects: ShardEffect[];
  description: string;
}

export interface PlayerShard {
  id: string;
  playerId: string;
  shardId: string;
  shard?: BlockchainShard;
  quantity: number;
  equipped: boolean;
  acquiredAt: Date;
}

export interface ShardEffect {
  type: 'MULTIPLIER' | 'FLAT_BONUS' | 'SPECIAL_ABILITY';
  target: string; // e.g., 'click_power', 'mining_output', 'cost_reduction'
  value: number;
  conditions?: string[]; // Optional conditions for effect activation
}

export interface PlayerResearch {
  id: string;
  playerId: string;
  researchId: string;
  level: number;
  isCompleted: boolean;
  startedAt: Date;
  completedAt?: Date;
}

export interface PlayerDataCenter {
  id: string;
  playerId: string;
  locationId: string;
  level: number;
  isActive: boolean;
  establishedAt: Date;
}

// Market Types
export interface CryptocurrencyPrice {
  id: string;
  currency: CurrencyType;
  price: Decimal;
  volume24h: Decimal;
  volatility: Decimal;
  timestamp: Date;
}

export interface MarketData {
  currencies: {
    [key in CurrencyType]?: {
      price: number;
      change24h: number;
      volume: number;
      volatility: number;
      name: string;
      symbol: string;
      color: string;
      isUnlocked: boolean;
      isEnabled: boolean;
    };
  };
  trends: MarketTrend[];
  events: ActiveMarketEvent[];
  activeMiningCurrency?: CurrencyType;
}

export interface MarketTrend {
  currency: CurrencyType;
  direction: 'UP' | 'DOWN' | 'STABLE';
  strength: number; // 0-1
  duration: number; // in seconds
}

export interface ActiveMarketEvent {
  id: string;
  name: string;
  description: string;
  effects: EventEffect[];
  remainingTime: number;
}

// Trading Types
export interface TradeListings {
  id: string;
  sellerId: string;
  seller?: PlayerState;
  shardId: string;
  shard?: BlockchainShard;
  price: Decimal;
  currency: CurrencyType;
  quantity: number;
  isActive: boolean;
  createdAt: Date;
  expiresAt?: Date;
}

export interface Trade {
  id: string;
  buyerId: string;
  buyer?: PlayerState;
  sellerId: string;
  seller?: PlayerState;
  shardId: string;
  price: Decimal;
  currency: CurrencyType;
  quantity: number;
  completedAt: Date;
}

// Social Features Types
export interface Syndicate {
  id: string;
  name: string;
  description?: string;
  leaderId: string;
  leader?: PlayerState;
  level: number;
  experience: bigint;
  members: SyndicateMember[];
  createdAt: Date;
}

export interface SyndicateMember {
  id: string;
  playerId: string;
  player?: PlayerState;
  syndicateId: string;
  syndicate?: Syndicate;
  role: SyndicateRole;
  joinedAt: Date;
  contribution: bigint;
}

export interface DAOProposal {
  id: string;
  title: string;
  description: string;
  proposerId: string;
  proposer?: PlayerState;
  votesFor: number;
  votesAgainst: number;
  status: ProposalStatus;
  createdAt: Date;
  expiresAt: Date;
  votes: DAOVote[];
}

export interface DAOVote {
  id: string;
  proposalId: string;
  proposal?: DAOProposal;
  voterId: string;
  voter?: PlayerState;
  vote: VoteChoice;
  weight: number;
  castAt: Date;
}

// Event System Types
export interface BlockchainEvent {
  id: string;
  type: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  name: string;
  description: string;
  effects: EventEffect[];
  duration: number; // in seconds
  probability: number;
  conditions?: string[]; // Conditions for event to trigger
}

export interface EventEffect {
  target: string; // What the event affects
  modifier: number; // Multiplier or flat change
  duration: number; // How long the effect lasts
}

// Enums (matching Prisma schema)
export enum RigType {
  USB_ASIC = 'USB_ASIC',
  GPU_FARM = 'GPU_FARM',
  ASIC_MINER = 'ASIC_MINER',
  QUANTUM_PROCESSOR = 'QUANTUM_PROCESSOR',
  FUSION_REACTOR = 'FUSION_REACTOR',
  DYSON_SPHERE = 'DYSON_SPHERE',
}

export enum ShardRarity {
  COMMON = 'COMMON',
  UNCOMMON = 'UNCOMMON',
  RARE = 'RARE',
  EPIC = 'EPIC',
  LEGENDARY = 'LEGENDARY',
  MYTHIC = 'MYTHIC',
}

export enum ShardCategory {
  MINING_BOOST = 'MINING_BOOST',
  EFFICIENCY = 'EFFICIENCY',
  MARKET_ADVANTAGE = 'MARKET_ADVANTAGE',
  SPECIAL_ABILITY = 'SPECIAL_ABILITY',
  PRESTIGE_BONUS = 'PRESTIGE_BONUS',
}

export enum CurrencyType {
  CRYPTO_COIN = 'CRYPTO_COIN',
  ETHEREUM_G = 'ETHEREUM_G',
  BYTE_COIN = 'BYTE_COIN',
  SOLANA_X = 'SOLANA_X',
  CARDANO_Z = 'CARDANO_Z',
  POLKADOT_Y = 'POLKADOT_Y',
  AVALANCHE_A = 'AVALANCHE_A',
  COSMOS_C = 'COSMOS_C',
  TEZOS_T = 'TEZOS_T',
  ALGORAND_ALGO = 'ALGORAND_ALGO',
}

export interface CurrencySettings {
  id: string;
  currency: CurrencyType;
  name: string;
  symbol: string;
  color: string;
  description: string;
  baseValue: Decimal;
  isEnabled: boolean;
  isUnlocked: boolean;
  unlockRequirement?: CurrencyUnlockRequirement;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlayerCurrencySettings {
  id: string;
  playerId: string;
  currency: CurrencyType;
  isUnlocked: boolean;
  isMiningTarget: boolean;
  lastMined?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CurrencyUnlockRequirement {
  type: 'RESEARCH' | 'CURRENCY' | 'PRESTIGE_LEVEL' | 'CLICKS';
  value: number;
  currencyType?: CurrencyType;
}

export enum SyndicateRole {
  LEADER = 'LEADER',
  OFFICER = 'OFFICER',
  MEMBER = 'MEMBER',
}

export enum ProposalStatus {
  ACTIVE = 'ACTIVE',
  PASSED = 'PASSED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
}

export enum VoteChoice {
  FOR = 'FOR',
  AGAINST = 'AGAINST',
  ABSTAIN = 'ABSTAIN',
}

// Game Configuration Types
export interface RigConfig {
  type: RigType;
  name: string;
  baseCost: number;
  baseOutput: number;
  costMultiplier: number;
  unlockRequirement?: {
    type: 'CLICKS' | 'CURRENCY' | 'RESEARCH';
    value: number;
  };
}

export interface ResearchConfig {
  id: string;
  name: string;
  description: string;
  cost: number;
  currency: CurrencyType;
  prerequisites: string[];
  unlocks: string[];
}

export interface LocationConfig {
  id: string;
  name: string;
  description: string;
  unlockCost: number;
  currency: CurrencyType;
  bonuses: {
    type: string;
    value: number;
  }[];
  requirements?: {
    type: string;
    value: number;
  }[];
}