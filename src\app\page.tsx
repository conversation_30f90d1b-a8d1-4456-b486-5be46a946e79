import Link from "next/link";

import { GameLayout } from "~/app/_components/layout/GameLayout";
import { Dashboard } from "~/app/_components/game/Dashboard";
import { auth } from "~/server/auth";
import { HydrateClient } from "~/trpc/server";

export default async function Home() {
	const session = await auth();

	if (!session?.user) {
		return (
			<HydrateClient>
				<main className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-[#2e026d] to-[#15162c] text-white">
					<div className="container flex flex-col items-center justify-center gap-12 px-4 py-16">
						<h1 className="font-extrabold text-5xl tracking-tight sm:text-[5rem]">
							Crypto <span className="text-[hsl(280,100%,70%)]">Tycoon</span>
						</h1>
						<p className="text-xl text-gray-300 text-center max-w-2xl">
							Build your blockchain mining empire and dominate the cryptocurrency market.
							Start clicking to mine your first CryptoCoin!
						</p>
						
						<div className="flex flex-col items-center justify-center gap-4">
							<Link
								href="/api/auth/signin"
								className="rounded-full bg-gradient-to-r from-purple-500 to-blue-500 px-10 py-3 font-semibold no-underline transition hover:from-purple-400 hover:to-blue-400 text-white shadow-lg"
							>
								Start Playing
							</Link>
							<p className="text-sm text-gray-400">
								Sign in to begin your crypto mining journey
							</p>
						</div>
					</div>
				</main>
			</HydrateClient>
		);
	}

	return (
		<HydrateClient>
			<GameLayout>
				<Dashboard />
			</GameLayout>
		</HydrateClient>
	);
}
