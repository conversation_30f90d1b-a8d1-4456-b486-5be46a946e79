import { Decimal } from "@prisma/client/runtime/library";
import { CurrencyType } from "../types/game";
import { RIG_CONFIGS, CURRENCY_CONFIGS } from "../config/game-constants";

/**
 * Convert Decimal to number safely
 */
export function decimalToNumber(decimal: Decimal | number | string): number {
  if (typeof decimal === 'number') return decimal;
  if (typeof decimal === 'string') return parseFloat(decimal);
  if (typeof decimal === 'object' && decimal !== null && 'toNumber' in decimal) {
    return decimal.toNumber();
  }
  return 0;
}

/**
 * Convert number to Decimal safely
 */
export function numberToDecimal(num: number): Decimal {
  return new Decimal(num);
}

/**
 * Check if player meets requirements for unlocking content
 */
export function checkUnlockRequirements(
  requirements: any[],
  playerState: any,
  playerResearch: any[],
  playerDataCenters: any[]
): boolean {
  for (const requirement of requirements) {
    switch (requirement.type) {
      case 'CLICKS':
        if (playerState.totalClicks < requirement.value) return false;
        break;
      case 'CURRENCY':
        // Map currency enum to property name
        const currencyKey = requirement.currency === CurrencyType.CRYPTO_COIN ? 'cryptoCoin' :
                           requirement.currency === CurrencyType.ETHEREUM_G ? 'ethereumG' :
                           requirement.currency === CurrencyType.BYTE_COIN ? 'byteCoin' :
                           requirement.currency === CurrencyType.SOLANA_X ? 'solanaX' :
                           requirement.currency === CurrencyType.CARDANO_Z ? 'cardanoZ' :
                           requirement.currency === CurrencyType.POLKADOT_Y ? 'polkadotY' :
                           'cryptoCoin'; // default fallback
        const currencyAmount = playerState.currencies[currencyKey];
        if (decimalToNumber(currencyAmount) < requirement.value) return false;
        break;
      case 'RESEARCH':
        const hasResearch = playerResearch.some(r => r.researchId === requirement.value && r.isCompleted);
        if (!hasResearch) return false;
        break;
      case 'LOCATION':
        const hasLocation = playerDataCenters.some(dc => dc.locationId === requirement.value && dc.isActive);
        if (!hasLocation) return false;
        break;
      case 'PRESTIGE_LEVEL':
        if (playerState.prestigeLevel < requirement.value) return false;
        break;
      default:
        return false;
    }
  }

  return true;
}

/**
 * Check if a mining rig type is unlocked for the player
 */
export function isRigUnlocked(
  rigType: string,
  playerState: any,
  playerResearch: any[] = [],
  playerDataCenters: any[] = []
): boolean {
  const config = RIG_CONFIGS[rigType as keyof typeof RIG_CONFIGS];
  if (!config || !config.unlockRequirement) return true;

  return checkUnlockRequirements(
    [config.unlockRequirement],
    playerState,
    playerResearch,
    playerDataCenters
  );
}

/**
 * Get all available rig types for a player based on unlock conditions
 */
export function getAvailableRigTypes(
  playerState: any,
  playerResearch: any[] = [],
  playerDataCenters: any[] = []
): string[] {
  return Object.keys(RIG_CONFIGS).filter(rigType =>
    isRigUnlocked(rigType, playerState, playerResearch, playerDataCenters)
  );
}

/**
 * Check if a specific currency unlock requirement is met
 */
export function checkCurrencyUnlockRequirement(
  requirement: any,
  playerState: any,
  playerResearch: any[]
): boolean {
  if (!requirement) return true;

  switch (requirement.type) {
    case 'RESEARCH':
      return playerResearch.some(r => r.researchId === `currency_${requirement.currencyType.toLowerCase()}` && r.isCompleted);
    case 'CURRENCY':
      const currencyKey = requirement.currencyType === CurrencyType.CRYPTO_COIN ? 'cryptoCoin' :
                         requirement.currencyType === CurrencyType.ETHEREUM_G ? 'ethereumG' :
                         requirement.currencyType === CurrencyType.BYTE_COIN ? 'byteCoin' :
                         requirement.currencyType === CurrencyType.SOLANA_X ? 'solanaX' :
                         requirement.currencyType === CurrencyType.CARDANO_Z ? 'cardanoZ' :
                         requirement.currencyType === CurrencyType.POLKADOT_Y ? 'polkadotY' :
                         'cryptoCoin'; // default fallback
      const currencyAmount = playerState.currencies[currencyKey];
      return decimalToNumber(currencyAmount) >= requirement.value;
    case 'PRESTIGE_LEVEL':
      return playerState.prestigeLevel >= requirement.value;
    case 'CLICKS':
      return playerState.totalClicks >= requirement.value;
    default:
      return false;
  }
}

/**
 * Check if a currency is unlocked for the player
 */
export function isCurrencyUnlocked(
  currencyType: CurrencyType,
  playerState: any,
  playerResearch: any[] = []
): boolean {
  const config = CURRENCY_CONFIGS[currencyType];
  if (!config || !config.unlockRequirement) return true;

  return checkCurrencyUnlockRequirement(config.unlockRequirement, playerState, playerResearch);
}

/**
 * Get all unlocked currencies for a player
 */
export function getUnlockedCurrencies(
  playerState: any,
  playerResearch: any[] = []
): CurrencyType[] {
  return Object.keys(CURRENCY_CONFIGS)
    .filter(currencyType =>
      isCurrencyUnlocked(currencyType as CurrencyType, playerState, playerResearch)
    ) as CurrencyType[];
}
