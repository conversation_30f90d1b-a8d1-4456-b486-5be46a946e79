"use client";

import { useEffect, useState } from "react";
import {
  CpuChipIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ClockIcon
} from "@heroicons/react/24/outline";
import { CurrencyDisplay } from "../ui/CurrencyDisplay";
import { ProgressBar } from "../ui/ProgressBar";
import { CurrencySwitcher } from "./CurrencySwitcher";
import { MarketOverview } from "./MarketOverview";
import { CurrencyType } from "~/types/game";
import { useGameData } from "~/stores/game-store";
import { useSmartPolling } from "~/hooks/useSmartPolling";

export function Dashboard() {
  const gameData = useGameData();
  const [timeOnline, setTimeOnline] = useState(0);

  // Update time online every second
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeOnline(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Smart polling - less frequent for dashboard
  useSmartPolling(gameData.refetch, {
    interval: 15000, // 15 seconds for dashboard
    pauseOnActivity: true,
    pauseOnHidden: true,
  });

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    }
    return `${minutes}m ${secs}s`;
  };

  const totalCPS = gameData.getTotalCPS();

  if (!gameData.player) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-400">Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-500/30 rounded-lg p-6">
        <h1 className="text-2xl font-bold text-white mb-2">
          Welcome to Crypto Tycoon!
        </h1>
        <p className="text-gray-300">
          Build your blockchain mining empire and dominate the cryptocurrency market.
        </p>
      </div>

      {/* Currency Switcher */}
      <CurrencySwitcher />
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Active Currency Balance */}
        <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-2">
            <div className="p-2 bg-orange-500/20 rounded-lg">
              <CurrencyDollarIcon className="w-5 h-5 text-orange-400" />
            </div>
            <div className="text-xs text-gray-400">Balance</div>
          </div>
          <CurrencyDisplay
            amount={gameData.getCurrentBalance()}
            currency={gameData.getActiveMiningCurrency()}
            showIcon
            className="text-lg"
          />
        </div>

        {/* CPS Rate */}
        <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-2">
            <div className="p-2 bg-green-500/20 rounded-lg">
              <ChartBarIcon className="w-5 h-5 text-green-400" />
            </div>
            <div className="text-xs text-gray-400">Per Second</div>
          </div>
          <CurrencyDisplay
            amount={totalCPS}
            currency={CurrencyType.CRYPTO_COIN}
            showIcon
            className="text-lg"
          />
        </div>

        {/* Total Clicks */}
        <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-2">
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <CpuChipIcon className="w-5 h-5 text-purple-400" />
            </div>
            <div className="text-xs text-gray-400">Total Clicks</div>
          </div>
          <div className="text-lg font-medium text-white">
            {gameData.player?.stats.totalClicks}
          </div>
        </div>

        {/* Time Online */}
        <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-2">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <ClockIcon className="w-5 h-5 text-blue-400" />
            </div>
            <div className="text-xs text-gray-400">Session Time</div>
          </div>
          <div className="text-lg font-medium text-white">
            {formatTime(timeOnline)}
          </div>
        </div>
      </div>

      {/* Mining Rigs Overview */}
      <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-6 backdrop-blur-sm">
        <h2 className="text-lg font-semibold text-white mb-4">Mining Operations</h2>

        {gameData.miningRigs.length === 0 ? (
          <div className="text-center py-8">
            <CpuChipIcon className="w-12 h-12 text-gray-500 mx-auto mb-3" />
            <p className="text-gray-400 mb-2">No mining rigs yet</p>
            <p className="text-sm text-gray-500">
              Start by clicking to mine CTC, then purchase your first mining rig!
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {gameData.miningRigs.map((rig) => (
              <div key={rig.id} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CpuChipIcon className="w-5 h-5 text-purple-400" />
                  <div>
                    <div className="font-medium text-white">{rig.rigType}</div>
                    <div className="text-sm text-gray-400">Level {rig.level} × {rig.quantity}</div>
                  </div>
                </div>
                <div className="text-right">
                  <CurrencyDisplay
                    amount={rig.baseOutput * rig.quantity * rig.efficiency}
                    currency={CurrencyType.CRYPTO_COIN}
                    className="text-sm"
                  />
                  <div className="text-xs text-gray-400">per second</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Market Overview and Progress */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Market Overview */}
        <MarketOverview />
        
        {/* Progress to Next Milestone */}
        <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-6 backdrop-blur-sm">
          <h2 className="text-lg font-semibold text-white mb-4">Progress</h2>

          <div className="space-y-4">
            {/* Example milestone progress */}
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-300">First Mining Rig</span>
                <span className="text-gray-400">
                  {gameData.getCurrentBalance() >= 100 ? "Complete!" : `${gameData.getCurrentBalance().toFixed(0)}/100 CTC`}
                </span>
              </div>
              <ProgressBar
                progress={Math.min(100, (gameData.getCurrentBalance() / 100) * 100)}
                color="green"
                size="sm"
              />
            </div>

            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-300">Click Master</span>
                <span className="text-gray-400">
                  {(gameData.player?.stats.totalClicks || 0) >= 1000 ? "Complete!" : `${gameData.player?.stats.totalClicks || 0}/1000 clicks`}
                </span>
              </div>
              <ProgressBar
                progress={Math.min(100, ((gameData.player?.stats.totalClicks || 0) / 1000) * 100)}
                color="purple"
                size="sm"
              />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-300">Currency Collector</span>
                <span className="text-gray-400">
                  {Object.values(gameData.marketData || {}).filter(c => c?.isUnlocked).length}/10 currencies
                </span>
              </div>
              <ProgressBar
                progress={Math.min(100, (Object.values(gameData.marketData || {}).filter(c => c?.isUnlocked).length / 10) * 100)}
                color="blue"
                size="sm"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}