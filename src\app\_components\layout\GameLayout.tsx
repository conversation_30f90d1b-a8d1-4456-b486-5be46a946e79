"use client";

import { useState } from "react";
import { Sidebar } from "./Sidebar";
import { Header } from "./Header";
import { NotificationSystem } from "./NotificationSystem";
import { PassiveIncomeDebug } from "../debug/PassiveIncomeDebug";
import { GameWebSocketProvider } from "../providers/GameWebSocketProvider";
import { usePassiveIncome } from "~/hooks/usePassiveIncome";

interface GameLayoutProps {
  children: React.ReactNode;
}

function GameLayoutContent({ children }: GameLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Initialize passive income for the entire app
  usePassiveIncome();

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Background with subtle pattern */}
      <div className="fixed inset-0 bg-gradient-to-br from-gray-900 via-purple-900/20 to-blue-900/20" />
      <div
        className="fixed inset-0 opacity-50"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}
      />

      <div className="relative flex h-screen">
        {/* Sidebar */}
        <Sidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />

        {/* Main Content */}
        <div className={`flex-1 flex flex-col transition-all duration-300 ${
          sidebarOpen ? 'ml-64' : 'ml-16'
        }`}>
          {/* Header */}
          <Header onMenuToggle={() => setSidebarOpen(!sidebarOpen)} />

          {/* Page Content */}
          <main className="flex-1 overflow-auto p-6">
            {children}
          </main>
        </div>
      </div>

      {/* Notification System */}
      <NotificationSystem />

      {/* Debug Components (only in development) */}
      {process.env.NODE_ENV === 'development' && <PassiveIncomeDebug />}
    </div>
  );
}

export function GameLayout({ children }: GameLayoutProps) {
  return (
    <GameWebSocketProvider>
      <GameLayoutContent>{children}</GameLayoutContent>
    </GameWebSocketProvider>
  );
}