import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Verifying database setup...');
  
  try {
    // Check Blockchain Shards
    const shardCount = await prisma.blockchainShard.count();
    console.log(`📦 Blockchain Shards: ${shardCount}`);
    
    // Check Cryptocurrency Prices
    const priceCount = await prisma.cryptocurrencyPrice.count();
    console.log(`💰 Cryptocurrency Prices: ${priceCount}`);
    
    // Show a sample shard
    const sampleShard = await prisma.blockchainShard.findFirst({
      where: { rarity: 'MYTHIC' }
    });
    
    if (sampleShard) {
      console.log(`🌟 Sample Mythic Shard: ${sampleShard.name}`);
      console.log(`   Description: ${sampleShard.description}`);
    }
    
    console.log('✅ Database verification complete!');
    
  } catch (error) {
    console.error('❌ Database verification failed:', error);
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });