-- AlterTable
ALTER TABLE "Player" ADD COLUMN "cardanoZ" DECIMAL(20,8) NOT NULL DEFAULT 0,
                     ADD COLUMN "polkadotY" DECIMAL(20,8) NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "CurrencySettings" (
    "id" TEXT NOT NULL,
    "currency" "CurrencyType" NOT NULL,
    "name" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "color" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "baseValue" DECIMAL(20,8) NOT NULL,
    "isEnabled" BOOLEAN NOT NULL DEFAULT true,
    "isUnlocked" BOOLEAN NOT NULL DEFAULT false,
    "unlockRequirement" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CurrencySettings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PlayerCurrencySettings" (
    "id" TEXT NOT NULL,
    "playerId" TEXT NOT NULL,
    "currency" "CurrencyType" NOT NULL,
    "isUnlocked" BOOLEAN NOT NULL DEFAULT false,
    "isMiningTarget" BOOLEAN NOT NULL DEFAULT false,
    "lastMined" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PlayerCurrencySettings_pkey" PRIMARY KEY ("id")
);

-- AlterEnum
ALTER TYPE "CurrencyType" ADD VALUE 'AVALANCHE_A';
ALTER TYPE "CurrencyType" ADD VALUE 'COSMOS_C';
ALTER TYPE "CurrencyType" ADD VALUE 'TEZOS_T';
ALTER TYPE "CurrencyType" ADD VALUE 'ALGORAND_ALGO';

-- CreateIndex
CREATE INDEX "CurrencySettings_isEnabled_idx" ON "CurrencySettings"("isEnabled");

-- CreateIndex
CREATE INDEX "CurrencySettings_isUnlocked_idx" ON "CurrencySettings"("isUnlocked");

-- CreateIndex
CREATE UNIQUE INDEX "CurrencySettings_currency_key" ON "CurrencySettings"("currency");

-- CreateIndex
CREATE INDEX "PlayerCurrencySettings_playerId_idx" ON "PlayerCurrencySettings"("playerId");

-- CreateIndex
CREATE INDEX "PlayerCurrencySettings_currency_idx" ON "PlayerCurrencySettings"("currency");

-- CreateIndex
CREATE INDEX "PlayerCurrencySettings_isMiningTarget_idx" ON "PlayerCurrencySettings"("isMiningTarget");

-- CreateIndex
CREATE UNIQUE INDEX "PlayerCurrencySettings_playerId_currency_key" ON "PlayerCurrencySettings"("playerId", "currency");

-- AddForeignKey
ALTER TABLE "PlayerCurrencySettings" ADD CONSTRAINT "PlayerCurrencySettings_playerId_fkey" FOREIGN KEY ("playerId") REFERENCES "Player"("id") ON DELETE CASCADE ON UPDATE CASCADE;