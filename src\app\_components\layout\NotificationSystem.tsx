"use client";

import { useState, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface Notification {
    id: string;
    type: 'success' | 'error' | 'warning' | 'info' | 'passive_income';
    title: string;
    message: string;
    duration?: number;
    amount?: number; // For passive income notifications
}

export function NotificationSystem() {
    const [notifications, setNotifications] = useState<Notification[]>([]);

    const removeNotification = (id: string) => {
        setNotifications(prev => prev.filter(n => n.id !== id));
    };

    const addNotification = (notification: Omit<Notification, 'id'>) => {
        const id = Math.random().toString(36).substring(2, 11);
        const newNotification = { ...notification, id };

        setNotifications(prev => [...prev, newNotification]);

        // Auto-remove after duration
        const duration = notification.duration || 5000;
        setTimeout(() => {
            removeNotification(id);
        }, duration);
    };

    // Listen for global notification events
    useEffect(() => {
        const handleNotification = (event: CustomEvent) => {
            addNotification(event.detail);
        };

        window.addEventListener('show_notification', handleNotification as EventListener);

        return () => {
            window.removeEventListener('show_notification', handleNotification as EventListener);
        };
    }, []);

    const getNotificationStyles = (type: Notification['type']) => {
        switch (type) {
            case 'success':
                return 'bg-green-500/20 border-green-500/50 text-green-300';
            case 'error':
                return 'bg-red-500/20 border-red-500/50 text-red-300';
            case 'warning':
                return 'bg-yellow-500/20 border-yellow-500/50 text-yellow-300';
            case 'passive_income':
                return 'bg-orange-500/20 border-orange-500/50 text-orange-300';
            case 'info':
            default:
                return 'bg-blue-500/20 border-blue-500/50 text-blue-300';
        }
    };

    return (
        <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
            {notifications.map((notification) => (
                <div
                    key={notification.id}
                    className={`
            p-4 rounded-lg border backdrop-blur-sm shadow-lg
            transform transition-all duration-300 ease-in-out
            ${getNotificationStyles(notification.type)}
          `}
                >
                    <div className="flex items-start justify-between">
                        <div className="flex-1">
                            <h4 className="font-medium text-sm">{notification.title}</h4>
                            <p className="text-xs mt-1 opacity-90">{notification.message}</p>
                            {notification.type === 'passive_income' && notification.amount && (
                                <div className="text-sm font-bold mt-1">
                                    +{notification.amount.toFixed(2)} CTC
                                </div>
                            )}
                        </div>
                        <button
                            onClick={() => removeNotification(notification.id)}
                            className="ml-2 p-1 rounded hover:bg-white/10 transition-colors"
                        >
                            <XMarkIcon className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            ))}
        </div>
    );
}