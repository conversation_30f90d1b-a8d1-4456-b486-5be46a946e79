"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useGameWebSocket } from '~/lib/websocket-client';
import { useGameStore } from '~/stores/game-store';
import { showPassiveIncomeNotification } from '~/lib/notifications';

interface GameWebSocketContextType {
  connected: boolean;
  error: string | null;
  sendClick: (timestamp?: number) => boolean;
  disconnect: () => void;
}

const GameWebSocketContext = createContext<GameWebSocketContextType | null>(null);

export function useGameWebSocketContext() {
  const context = useContext(GameWebSocketContext);
  if (!context) {
    throw new Error('useGameWebSocketContext must be used within a GameWebSocketProvider');
  }
  return context;
}

interface GameWebSocketProviderProps {
  children: React.ReactNode;
}

export function GameWebSocketProvider({ children }: GameWebSocketProviderProps) {
  const { data: session, status } = useSession();
  const player = useGameStore(state => state.player);
  const updateFromServer = useGameStore(state => state.updateFromServer);
  const forceServerSync = useGameStore(state => state.forceServerSync);
  const miningRigs = useGameStore(state => state.miningRigs);

  // Determine user ID more carefully to avoid race conditions
  const userId = React.useMemo(() => {
    // Wait for session to be loaded before deciding
    if (status === 'loading') {
      return null; // Don't connect yet
    }

    if (status === 'authenticated' && session?.user?.id) {
      console.log('🔐 Using authenticated user ID:', session.user.id);
      return session.user.id;
    }

    if (player?.userId) {
      console.log('🔐 Using player user ID:', player.userId);
      return player.userId;
    }

    console.log('🔐 Using anonymous user');
    return 'anonymous';
  }, [status, session?.user?.id, player?.userId]);

  const { connected, error, sendClick, disconnect } = useGameWebSocket(userId);

  // Listen for WebSocket events and update the game store
  useEffect(() => {
    const handleClickAcknowledged = (event: CustomEvent) => {
      const { newBalance } = event.detail;

      // Update store with server response
      if (player) {
        updateFromServer({
          player: {
            ...player,
            currencies: {
              ...player.currencies,
              cryptoCoin: newBalance,
            },
          },
          miningRigs,
        });
      }
    };

    const handleGameUpdate = (event: CustomEvent) => {
      const { balance, totalClicks, cps, passiveIncome } = event.detail;

      console.log(`🔄 WebSocket game update received - Balance: ${balance}, Clicks: ${totalClicks}, CPS: ${cps}, Passive: ${passiveIncome || 'N/A'}`);

      if (player) {
        // Check if this is a passive income update
        const currentBalance = player.currencies.cryptoCoin;
        const balanceIncrease = balance - currentBalance;

        // If we have a significant balance increase and it's marked as passive income, show notification
        if (passiveIncome && passiveIncome > 0) {
          console.log(`💰 Showing passive income notification: +${passiveIncome} CTC`);
          showPassiveIncomeNotification(passiveIncome);
        }

        // Use forceServerSync for game updates from server (passive income, etc.)
        // as these are authoritative and should override optimistic state
        forceServerSync({
          player: {
            ...player,
            currencies: {
              ...player.currencies,
              cryptoCoin: balance,
            },
            stats: {
              ...player.stats,
              totalClicks,
              cpsRate: cps,
            },
          },
          miningRigs,
        });
      }
    };

    const handleRateLimitWarning = (event: CustomEvent) => {
      console.warn('⚠️ Rate limit warning:', event.detail.message);
      // Could show a toast notification here
    };

    const handleError = (event: CustomEvent) => {
      console.error('🚨 WebSocket error:', event.detail.message);
      // Could show an error notification here
    };

    // Add event listeners
    window.addEventListener('click_acknowledged', handleClickAcknowledged as EventListener);
    window.addEventListener('game_update', handleGameUpdate as EventListener);
    window.addEventListener('rate_limit_warning', handleRateLimitWarning as EventListener);
    window.addEventListener('game_error', handleError as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('click_acknowledged', handleClickAcknowledged as EventListener);
      window.removeEventListener('game_update', handleGameUpdate as EventListener);
      window.removeEventListener('rate_limit_warning', handleRateLimitWarning as EventListener);
      window.removeEventListener('game_error', handleError as EventListener);
    };
  }, [player, miningRigs, updateFromServer, forceServerSync]);

  // Log connection status changes
  useEffect(() => {
    if (connected) {
      console.log('🔗 Site-wide WebSocket connected for user:', userId);
    } else {
      console.log('🔌 Site-wide WebSocket disconnected for user:', userId);
    }
  }, [connected, userId]);

  const contextValue: GameWebSocketContextType = {
    connected,
    error,
    sendClick,
    disconnect,
  };

  return (
    <GameWebSocketContext.Provider value={contextValue}>
      {children}
    </GameWebSocketContext.Provider>
  );
}
